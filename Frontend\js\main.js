/**
 * Main JavaScript file for the Water Supply Management Board website
 * This file contains functionality for the slider and other interactive elements
 */

// Wait for components to be loaded before initializing functionality
document.addEventListener('DOMContentLoaded', function() {
    // Components are loaded by components.js
    // The slider and mobile menu will be initialized after components are loaded
});

/**
 * Initialize the image slider
 */
function initSlider() {
    const sliderDots = document.querySelectorAll('.slider-dot');
    const sliderItems = document.querySelectorAll('.slider-item');

    // Set up click events for slider dots
    sliderDots.forEach((dot, index) => {
        dot.addEventListener('click', () => {
            // Remove active class from all dots and items
            sliderDots.forEach(d => d.classList.remove('active'));
            sliderItems.forEach(item => item.classList.remove('active'));

            // Add active class to current dot and item
            dot.classList.add('active');
            if (sliderItems[index]) {
                sliderItems[index].classList.add('active');
            }
        });
    });

    // Auto-rotate slider every 5 seconds
    let currentIndex = 0;
    setInterval(() => {
        currentIndex = (currentIndex + 1) % sliderDots.length;
        sliderDots[currentIndex].click();
    }, 5000);
}

/**
 * Initialize mobile menu toggle functionality
 */
function initMobileMenu() {
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');

    if (mobileMenuButton && mobileMenu) {
        mobileMenuButton.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
        });
    }
}
