import ExampleForm from './ExampleForm.vue'
import VCheckbox from './VCheckbox.vue'
import VForm from './VForm.vue'
import VInput from './VInput.vue'
import VRadio from './VRadio.vue'
import VSelect from './VSelect.vue'
import VTextarea from './VTextarea.vue'

// Export individual components
export { ExampleForm, VCheckbox, VForm, VInput, VRadio, VSelect, VTextarea }

// Export as plugin
export default {
  install(app) {
    app.component('VForm', VForm)
    app.component('VInput', VInput)
    app.component('VSelect', VSelect)
    app.component('VTextarea', VTextarea)
    app.component('VCheckbox', VCheckbox)
    app.component('VRadio', VRadio)
    app.component('ExampleForm', ExampleForm)
  }
}
