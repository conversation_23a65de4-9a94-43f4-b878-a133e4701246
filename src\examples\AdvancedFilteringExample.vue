<template>
  <div class="advanced-filtering-example">
    <h1>Advanced Filtering Example</h1>
    
    <data-table
      :items="users"
      :columns="columns"
      title="Users with Advanced Filtering"
      :loading="loading"
      :server-side="true"
      :total-items="totalItems"
      :current-page-server="currentPage"
      :page-size="pageSize"
      :show-column-filters="true"
      :filterable-columns="['status', 'role', 'created_at', 'name']"
      :filter-config="filterConfig"
      @page-change="handlePageChange"
      @page-size-change="handlePageSizeChange"
      @sort="handleSort"
      @search="handleSearch"
      @column-filter="handleColumnFilter"
      theme="primary"
    >
      <!-- Custom cell rendering for status -->
      <template #cell-status="{ value }">
        <span :class="['status-badge', `status-${value.toLowerCase()}`]">
          {{ value }}
        </span>
      </template>
      
      <!-- Custom cell rendering for created_at -->
      <template #cell-created_at="{ value }">
        {{ formatDate(value) }}
      </template>
      
      <!-- Actions column -->
      <template #actions="{ item }">
        <div class="action-buttons">
          <button class="btn-view" @click="viewUser(item)">
            <i class="fas fa-eye"></i> View
          </button>
          <button class="btn-edit" @click="editUser(item)">
            <i class="fas fa-edit"></i> Edit
          </button>
        </div>
      </template>
    </data-table>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import axios from 'axios';
import DataTable from '../plugins/vue-data-table/components/DataTable.vue';

// Column definitions
const columns = [
  { key: 'id', label: 'ID' },
  { key: 'name', label: 'Name', sortable: true },
  { key: 'email', label: 'Email', sortable: true },
  { key: 'role', label: 'Role', sortable: true },
  { key: 'status', label: 'Status' },
  { key: 'created_at', label: 'Created At', sortable: true }
];

// Filter configuration
const filterConfig = {
  status: {
    type: 'select',
    options: [
      { value: 'Active', label: 'Active' },
      { value: 'Inactive', label: 'Inactive' },
      { value: 'Pending', label: 'Pending' }
    ]
  },
  role: {
    type: 'select',
    options: [
      { value: 'Admin', label: 'Admin' },
      { value: 'User', label: 'User' },
      { value: 'Editor', label: 'Editor' },
      { value: 'Viewer', label: 'Viewer' }
    ]
  },
  created_at: {
    type: 'daterange'
  },
  name: {
    type: 'text'
  }
};

// State variables
const users = ref([]);
const loading = ref(false);
const totalItems = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);
const sortKey = ref('id');
const sortOrder = ref('desc');
const searchQuery = ref('');
const columnFilters = ref({});

// Sample data (in a real app, this would come from an API)
const sampleUsers = [
  { id: 1, name: 'John Doe', email: '<EMAIL>', role: 'Admin', status: 'Active', created_at: '2023-01-15' },
  { id: 2, name: 'Jane Smith', email: '<EMAIL>', role: 'User', status: 'Active', created_at: '2023-02-20' },
  { id: 3, name: 'Bob Johnson', email: '<EMAIL>', role: 'Editor', status: 'Inactive', created_at: '2023-03-10' },
  { id: 4, name: 'Alice Brown', email: '<EMAIL>', role: 'User', status: 'Pending', created_at: '2023-04-05' },
  { id: 5, name: 'Charlie Wilson', email: '<EMAIL>', role: 'Viewer', status: 'Active', created_at: '2023-05-12' },
  { id: 6, name: 'Diana Miller', email: '<EMAIL>', role: 'Editor', status: 'Active', created_at: '2023-06-18' },
  { id: 7, name: 'Edward Davis', email: '<EMAIL>', role: 'User', status: 'Inactive', created_at: '2023-07-22' },
  { id: 8, name: 'Fiona Clark', email: '<EMAIL>', role: 'Viewer', status: 'Pending', created_at: '2023-08-30' },
  { id: 9, name: 'George White', email: '<EMAIL>', role: 'Admin', status: 'Active', created_at: '2023-09-14' },
  { id: 10, name: 'Hannah Green', email: '<EMAIL>', role: 'User', status: 'Active', created_at: '2023-10-25' },
  { id: 11, name: 'Ian Black', email: '<EMAIL>', role: 'Editor', status: 'Inactive', created_at: '2023-11-05' },
  { id: 12, name: 'Julia Reed', email: '<EMAIL>', role: 'Viewer', status: 'Active', created_at: '2023-12-12' },
  { id: 13, name: 'Kevin Taylor', email: '<EMAIL>', role: 'User', status: 'Pending', created_at: '2024-01-08' },
  { id: 14, name: 'Laura Adams', email: '<EMAIL>', role: 'Admin', status: 'Active', created_at: '2024-02-19' },
  { id: 15, name: 'Mike Wilson', email: '<EMAIL>', role: 'Editor', status: 'Inactive', created_at: '2024-03-27' },
];

// Fetch users (simulated API call)
async function fetchUsers() {
  loading.value = true;
  
  try {
    // In a real app, this would be an API call
    // Simulate server-side processing
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Apply filters to sample data
    let filteredUsers = [...sampleUsers];
    
    // Apply search
    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase();
      filteredUsers = filteredUsers.filter(user => 
        user.name.toLowerCase().includes(query) || 
        user.email.toLowerCase().includes(query)
      );
    }
    
    // Apply column filters
    if (Object.keys(columnFilters.value).length > 0) {
      filteredUsers = filteredUsers.filter(user => {
        return Object.entries(columnFilters.value).every(([column, filter]) => {
          const value = user[column];
          
          if (column === 'created_at' && (filter.start || filter.end)) {
            const userDate = new Date(value);
            const startDate = filter.start ? new Date(filter.start) : null;
            const endDate = filter.end ? new Date(filter.end) : null;
            
            if (startDate && endDate) {
              return userDate >= startDate && userDate <= endDate;
            } else if (startDate) {
              return userDate >= startDate;
            } else if (endDate) {
              return userDate <= endDate;
            }
            return true;
          } else {
            return typeof filter === 'string' ? 
              value.toLowerCase().includes(filter.toLowerCase()) : 
              true;
          }
        });
      });
    }
    
    // Apply sorting
    if (sortKey.value) {
      filteredUsers.sort((a, b) => {
        const aValue = a[sortKey.value];
        const bValue = b[sortKey.value];
        
        if (aValue === bValue) return 0;
        
        const result = aValue > bValue ? 1 : -1;
        return sortOrder.value === 'asc' ? result : -result;
      });
    }
    
    // Apply pagination
    const start = (currentPage.value - 1) * pageSize.value;
    const end = start + pageSize.value;
    const paginatedUsers = filteredUsers.slice(start, end);
    
    // Update state
    users.value = paginatedUsers;
    totalItems.value = filteredUsers.length;
    
  } catch (error) {
    console.error('Error fetching users:', error);
  } finally {
    loading.value = false;
  }
}

// Event handlers
function handlePageChange(page) {
  currentPage.value = page;
  fetchUsers();
}

function handlePageSizeChange(size) {
  pageSize.value = size;
  currentPage.value = 1;
  fetchUsers();
}

function handleSort({ key, order }) {
  sortKey.value = key;
  sortOrder.value = order;
  fetchUsers();
}

function handleSearch(query) {
  searchQuery.value = query;
  currentPage.value = 1;
  fetchUsers();
}

function handleColumnFilter(filters) {
  columnFilters.value = filters;
  currentPage.value = 1;
  fetchUsers();
}

// Action handlers
function viewUser(user) {
  alert(`Viewing user: ${user.name}`);
}

function editUser(user) {
  alert(`Editing user: ${user.name}`);
}

// Helper functions
function formatDate(dateString) {
  if (!dateString) return '';
  
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
}

// Load initial data
onMounted(() => {
  fetchUsers();
});
</script>

<style scoped>
.advanced-filtering-example {
  padding: 20px;
}

h1 {
  margin-bottom: 20px;
  font-size: 24px;
  color: #1e293b;
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-active {
  background-color: #dcfce7;
  color: #166534;
}

.status-inactive {
  background-color: #fee2e2;
  color: #991b1b;
}

.status-pending {
  background-color: #fef3c7;
  color: #92400e;
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.action-buttons button {
  padding: 4px 8px;
  border-radius: 4px;
  border: none;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 4px;
}

.btn-view {
  background-color: #eff6ff;
  color: #2563eb;
}

.btn-edit {
  background-color: #f0fdf4;
  color: #16a34a;
}

.btn-view:hover {
  background-color: #dbeafe;
}

.btn-edit:hover {
  background-color: #dcfce7;
}
</style>
