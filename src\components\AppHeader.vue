<script setup>
import SidebarToggle from './SidebarToggle.vue'
</script>
<template>
  <header class="header-top">
    <nav class="navbar navbar-light">
      <div class="navbar-left">
        <div class="logo-area">
          <router-link to="/" class="navbar-brand">
            <!-- <img class="dark" src="/img/logo-dark.png" alt="logo"> -->
            <img class="dark" src="/img/bussewaLogo.png" alt="logo">

            <!-- <img class="light" src="/img/logo-white.png" alt="logo"> -->
          </router-link>
          <SidebarToggle />
        </div>
        <slot name="top-menu"></slot>
      </div>
      <slot name="navbar-right"></slot>
    </nav>
  </header>
</template>
