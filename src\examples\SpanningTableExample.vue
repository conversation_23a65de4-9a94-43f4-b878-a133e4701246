<template>
  <div class="spanning-table-example">
    <h1>Table with Column Spanning</h1>
    <p class="description">
      This example demonstrates how to use the DataTable component with column spanning/grouping.
      The table shows bus route data with grouped columns for "Previous" and "Current" data,
      as well as "Increment Ratio" metrics.
    </p>

    <div class="example-card">
      <h2>Bus Routes Data</h2>
      
      <data-table
        :items="busRoutes"
        :columns="columns"
        title="Bus Routes Performance"
        :loading="loading"
        :server-side="false"
        theme="primary"
      >
        <!-- Custom cell rendering for ratios -->
        <template #cell-ticket_increment_ratio="{ value }">
          <span :class="['ratio-badge', getRatioClass(value)]">
            {{ formatRatio(value) }}
          </span>
        </template>
        
        <template #cell-sales_increment_ratio="{ value }">
          <span :class="['ratio-badge', getRatioClass(value)]">
            {{ formatRatio(value) }}
          </span>
        </template>
        
        <template #cell-complaint_ratio="{ value }">
          <span :class="['ratio-badge', getRatioClass(value, true)]">
            {{ formatRatio(value) }}
          </span>
        </template>
      </data-table>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import DataTable from '../plugins/vue-data-table/components/DataTable.vue';

// Loading state
const loading = ref(true);

// Column definitions with grouping
const columns = [
  { key: 'sn', label: 'S.N', rowspan: 2 },
  { key: 'route', label: 'Route', rowspan: 2 },
  { key: 'bus_no', label: 'Bus No.', rowspan: 2 },
  { 
    label: 'Previous', 
    colspan: 4,
    children: [
      { key: 'prev_total_seats', label: 'Total Seats' },
      { key: 'prev_available', label: 'Available' },
      { key: 'prev_seat_count', label: 'Seat Count' },
      { key: 'prev_sales', label: 'Sales' }
    ]
  },
  { 
    label: 'Current', 
    colspan: 4,
    children: [
      { key: 'curr_total_seats', label: 'Total Seats' },
      { key: 'curr_available', label: 'Available' },
      { key: 'curr_seat_count', label: 'Seat Count' },
      { key: 'curr_sales', label: 'Sales' }
    ]
  },
  {
    label: 'Increment Ratio',
    colspan: 3,
    children: [
      { key: 'ticket_increment_ratio', label: 'Ticket Increment Ratio' },
      { key: 'sales_increment_ratio', label: 'Sales Increment Ratio' },
      { key: 'complaint_ratio', label: 'Complaint Ratio' }
    ]
  }
];

// Sample data
const busRoutes = ref([
  {
    sn: 1,
    route: 'Kathmandu - Pokhara',
    bus_no: 'BA-1-KHA-2345',
    prev_total_seats: 40,
    prev_available: 35,
    prev_seat_count: 32,
    prev_sales: 12800,
    curr_total_seats: 45,
    curr_available: 42,
    curr_seat_count: 40,
    curr_sales: 18000,
    ticket_increment_ratio: 0.25,
    sales_increment_ratio: 0.41,
    complaint_ratio: -0.15
  },
  {
    sn: 2,
    route: 'Kathmandu - Chitwan',
    bus_no: 'BA-2-KHA-1234',
    prev_total_seats: 35,
    prev_available: 30,
    prev_seat_count: 28,
    prev_sales: 8400,
    curr_total_seats: 35,
    curr_available: 32,
    curr_seat_count: 30,
    curr_sales: 9600,
    ticket_increment_ratio: 0.07,
    sales_increment_ratio: 0.14,
    complaint_ratio: 0.05
  },
  {
    sn: 3,
    route: 'Kathmandu - Butwal',
    bus_no: 'LU-1-KHA-5678',
    prev_total_seats: 45,
    prev_available: 40,
    prev_seat_count: 38,
    prev_sales: 19000,
    curr_total_seats: 50,
    curr_available: 48,
    curr_seat_count: 45,
    curr_sales: 24750,
    ticket_increment_ratio: 0.18,
    sales_increment_ratio: 0.30,
    complaint_ratio: -0.25
  },
  {
    sn: 4,
    route: 'Pokhara - Lumbini',
    bus_no: 'GA-1-KHA-9012',
    prev_total_seats: 30,
    prev_available: 25,
    prev_seat_count: 22,
    prev_sales: 6600,
    curr_total_seats: 30,
    curr_available: 28,
    curr_seat_count: 25,
    curr_sales: 8000,
    ticket_increment_ratio: 0.14,
    sales_increment_ratio: 0.21,
    complaint_ratio: -0.10
  },
  {
    sn: 5,
    route: 'Kathmandu - Biratnagar',
    bus_no: 'KO-1-KHA-3456',
    prev_total_seats: 40,
    prev_available: 38,
    prev_seat_count: 35,
    prev_sales: 14000,
    curr_total_seats: 40,
    curr_available: 36,
    curr_seat_count: 32,
    curr_sales: 13440,
    ticket_increment_ratio: -0.09,
    sales_increment_ratio: -0.04,
    complaint_ratio: 0.20
  }
]);

// Helper functions
function formatRatio(value) {
  const percent = (value * 100).toFixed(1);
  return `${percent > 0 ? '+' : ''}${percent}%`;
}

function getRatioClass(value, isComplaint = false) {
  // For complaint ratio, negative is good
  if (isComplaint) {
    if (value < -0.1) return 'ratio-good';
    if (value > 0.1) return 'ratio-bad';
    return 'ratio-neutral';
  }
  
  // For other ratios, positive is good
  if (value > 0.1) return 'ratio-good';
  if (value < -0.1) return 'ratio-bad';
  return 'ratio-neutral';
}

// Simulate loading
onMounted(() => {
  setTimeout(() => {
    loading.value = false;
  }, 1000);
});
</script>

<style>
.spanning-table-example {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

h1 {
  margin-bottom: 16px;
  color: #1e293b;
}

.description {
  margin-bottom: 24px;
  color: #64748b;
  font-size: 16px;
  line-height: 1.5;
}

.example-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 24px;
  margin-bottom: 32px;
  overflow: hidden;
}

h2 {
  margin-top: 0;
  margin-bottom: 16px;
  color: #1e293b;
  font-size: 20px;
}

.ratio-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.ratio-good {
  background-color: #dcfce7;
  color: #166534;
}

.ratio-bad {
  background-color: #fee2e2;
  color: #991b1b;
}

.ratio-neutral {
  background-color: #f1f5f9;
  color: #475569;
}
</style>
