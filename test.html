<html lang="hi">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Animated Concentric Circles with Realistic Space Background</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Devanagari&display=swap');

    body {
      margin: 0;
      padding: 1rem;
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      font-family: 'Noto Sans Devanagari', serif;
      background: radial-gradient(ellipse at center, #0b0c1a 0%, #000000 80%);
      overflow: hidden;
      position: relative;
    }

    /* Realistic starfield background with shining stars */
    body::before {
      content: "";
      position: fixed;
      top: 0; left: 0; right: 0; bottom: 0;
      background: black;
      z-index: -2;
    }
    /* Multiple layers of stars with different sizes and twinkle animations */
    .stars, .stars2, .stars3 {
      position: fixed;
      top: 0; left: 0; right: 0; bottom: 0;
      pointer-events: none;
      z-index: -1;
      background-repeat: repeat;
      background-position: 0 0;
      animation-timing-function: linear;
      animation-iteration-count: infinite;
    }
    .stars {
      background-image: radial-gradient(2px 2px at 20% 30%, #fff, transparent),
                        radial-gradient(1.5px 1.5px at 40% 70%, #fff, transparent),
                        radial-gradient(1.2px 1.2px at 60% 20%, #fff, transparent),
                        radial-gradient(2.5px 2.5px at 80% 50%, #fff, transparent),
                        radial-gradient(1.8px 1.8px at 30% 80%, #fff, transparent),
                        radial-gradient(1.3px 1.3px at 50% 50%, #fff, transparent),
                        radial-gradient(2px 2px at 70% 75%, #fff, transparent),
                        radial-gradient(2px 2px at 10% 10%, #fff, transparent),
                        radial-gradient(1.5px 1.5px at 90% 90%, #fff, transparent),
                        radial-gradient(1.2px 1.2px at 80% 20%, #fff, transparent),
                        radial-gradient(2.5px 2.5px at 60% 80%, #fff, transparent),
                        radial-gradient(1.8px 1.8px at 20% 60%, #fff, transparent),
                        radial-gradient(1.3px 1.3px at 35% 35%, #fff, transparent),
                        radial-gradient(2px 2px at 55% 65%, #fff, transparent);
      background-size: 100% 100%;
      animation-name: twinkleStars;
      animation-duration: 6s;
      opacity: 0.8;
    }
    .stars2 {
      background-image: radial-gradient(1.5px 1.5px at 10% 20%, #eee, transparent),
                        radial-gradient(1px 1px at 30% 60%, #eee, transparent),
                        radial-gradient(0.8px 0.8px at 50% 10%, #eee, transparent),
                        radial-gradient(1.8px 1.8px at 70% 40%, #eee, transparent),
                        radial-gradient(1.2px 1.2px at 90% 70%, #eee, transparent),
                        radial-gradient(1.5px 1.5px at 15% 80%, #eee, transparent),
                        radial-gradient(1.2px 1.2px at 80% 30%, #eee, transparent),
                        radial-gradient(1.8px 1.8px at 60% 60%, #eee, transparent),
                        radial-gradient(1px 1px at 40% 40%, #eee, transparent),
                        radial-gradient(0.8px 0.8px at 70% 90%, #eee, transparent);
      background-size: 100% 100%;
      animation-name: twinkleStars;
      animation-duration: 8s;
      opacity: 0.6;
    }
    .stars3 {
      background-image: radial-gradient(1px 1px at 15% 25%, #ccc, transparent),
                        radial-gradient(0.7px 0.7px at 35% 65%, #ccc, transparent),
                        radial-gradient(0.5px 0.5px at 55% 15%, #ccc, transparent),
                        radial-gradient(1.2px 1.2px at 75% 45%, #ccc, transparent),
                        radial-gradient(0.9px 0.9px at 95% 75%, #ccc, transparent),
                        radial-gradient(1px 1px at 25% 85%, #ccc, transparent),
                        radial-gradient(0.7px 0.7px at 65% 35%, #ccc, transparent),
                        radial-gradient(0.5px 0.5px at 85% 55%, #ccc, transparent),
                        radial-gradient(1.2px 1.2px at 45% 75%, #ccc, transparent),
                        radial-gradient(0.9px 0.9px at 75% 95%, #ccc, transparent);
      background-size: 100% 100%;
      animation-name: twinkleStars;
      animation-duration: 10s;
      opacity: 0.4;
    }

    @keyframes twinkleStars {
      0%, 100% {
        opacity: 0.6;
      }
      50% {
        opacity: 1;
      }
    }

    svg {
      max-width: 100%;
      height: auto;
      display: block;
      filter: drop-shadow(0 0 8px rgba(255,255,255,0.9));
      z-index: 1;
      position: relative;
    }

    /* Fade-in animation for circles */
    @keyframes fadeIn {
      0% {
        opacity: 0;
        transform: scale(0.5);
      }
      100% {
        opacity: 1;
        transform: scale(1);
      }
    }
    /* Rotation animations with different speeds and directions */
    @keyframes rotateClockwise {
      0% {
        transform: rotate(0deg);
      }
      100% {
        transform: rotate(360deg);
      }
    }
    @keyframes rotateCounterClockwise {
      0% {
        transform: rotate(0deg);
      }
      100% {
        transform: rotate(-360deg);
      }
    }
    /* Wrapper groups for rotation */
    .rotate-cw {
      transform-origin: 300px 300px;
      animation-timing-function: linear;
      animation-iteration-count: infinite;
      animation-name: rotateClockwise;
    }
    .rotate-ccw {
      transform-origin: 300px 300px;
      animation-timing-function: linear;
      animation-iteration-count: infinite;
      animation-name: rotateCounterClockwise;
    }
    /* Circle fade-in with delay */
    .circle {
      opacity: 0;
      transform-origin: 300px 300px;
      animation-fill-mode: forwards;
      animation-name: fadeIn;
      animation-duration: 0.8s;
      animation-timing-function: ease-out;
    }
    /* Different stroke dasharray for border styles and colors */
    .dash1 {
      stroke-dasharray: 15 10;
      stroke: #ff6f61; /* coral */
      filter: drop-shadow(0 0 8px #ff6f61);
    }
    .dash2 {
      stroke-dasharray: 5 15;
      stroke: #6ec1e4; /* sky blue */
      filter: drop-shadow(0 0 8px #6ec1e4);
    }
    .dash3 {
      stroke-dasharray: 25 5;
      stroke: #f9d71c; /* bright yellow */
      filter: drop-shadow(0 0 8px #f9d71c);
    }
    .dash4 {
      stroke-dasharray: 10 10;
      stroke: #9b59b6; /* purple */
      filter: drop-shadow(0 0 8px #9b59b6);
    }
    .dash5 {
      stroke-dasharray: 30 10;
      stroke: #1abc9c; /* turquoise */
      filter: drop-shadow(0 0 8px #1abc9c);
    }
    .dash6 {
      stroke-dasharray: 8 12;
      stroke: #e67e22; /* orange */
      filter: drop-shadow(0 0 8px #e67e22);
    }
    .dash7 {
      stroke-dasharray: 20 20;
      stroke: #ecf0f1; /* light gray */
      filter: drop-shadow(0 0 8px #ecf0f1);
    }
    .dash8 {
      stroke-dasharray: 12 8;
      stroke: #3498db; /* blue */
      filter: drop-shadow(0 0 8px #3498db);
    }
    .dash9 {
      stroke-dasharray: 18 6;
      stroke: #e84393; /* pink */
      filter: drop-shadow(0 0 8px #e84393);
    }
  </style>
</head>
<body>
  <div class="stars"></div>
  <div class="stars2"></div>
  <div class="stars3"></div>

  <svg
    width="600"
    height="600"
    viewBox="0 0 600 600"
    role="img"
    aria-label="Animated concentric circles diagram with galaxy background"
  >
    <line x1="20" y1="300" x2="580" y2="300" stroke="#ccc" stroke-width="1" />
    <!-- Circles wrapped in groups for rotation with increased delays -->
    <g class="rotate-cw" style="animation-duration: 40s; animation-delay: 0s;">
      <circle
        class="circle dash1"
        cx="300"
        cy="300"
        r="280"
        stroke-width="2"
        fill="none"
        style="animation-delay: 0s;"
      />
    </g>
    <g class="rotate-ccw" style="animation-duration: 35s; animation-delay: 1.2s;">
      <circle
        class="circle dash2"
        cx="300"
        cy="300"
        r="240"
        stroke-width="2"
        fill="none"
        style="animation-delay: 1.2s;"
      />
    </g>
    <g class="rotate-cw" style="animation-duration: 30s; animation-delay: 2.4s;">
      <circle
        class="circle dash3"
        cx="300"
        cy="300"
        r="200"
        stroke-width="2"
        fill="none"
        style="animation-delay: 2.4s;"
      />
    </g>
    <g class="rotate-ccw" style="animation-duration: 25s; animation-delay: 3.6s;">
      <circle
        class="circle dash4"
        cx="300"
        cy="300"
        r="160"
        stroke-width="2"
        fill="none"
        style="animation-delay: 3.6s;"
      />
    </g>
    <g class="rotate-cw" style="animation-duration: 20s; animation-delay: 4.8s;">
      <circle
        class="circle dash5"
        cx="300"
        cy="300"
        r="120"
        stroke-width="2"
        fill="none"
        style="animation-delay: 4.8s;"
      />
    </g>
    <g class="rotate-ccw" style="animation-duration: 15s; animation-delay: 6s;">
      <circle
        class="circle dash6"
        cx="300"
        cy="300"
        r="90"
        stroke-width="2"
        fill="none"
        style="animation-delay: 6s;"
      />
    </g>
    <g class="rotate-cw" style="animation-duration: 12s; animation-delay: 7.2s;">
      <circle
        class="circle dash7"
        cx="300"
        cy="300"
        r="60"
        stroke-width="2"
        fill="none"
        style="animation-delay: 7.2s;"
      />
    </g>
    <g class="rotate-ccw" style="animation-duration: 10s; animation-delay: 8.4s;">
      <circle
        class="circle dash8"
        cx="300"
        cy="300"
        r="40"
        stroke-width="2"
        fill="none"
        style="animation-delay: 8.4s;"
      />
    </g>
    <g class="rotate-cw" style="animation-duration: 8s; animation-delay: 9.6s;">
      <circle
        class="circle dash9"
        cx="300"
        cy="300"
        r="20"
        stroke-width="2"
        fill="none"
        style="animation-delay: 9.6s;"
      />
    </g>
  </svg>
</body>
</html>