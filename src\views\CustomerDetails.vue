    <template>
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <!-- Customer Profile Card -->
                    <div class="card mb-4">
                        <div class="card-body p-0">
                            <div class="row g-0">
                                <!-- Customer Profile Image and Basic Info -->
                                <div class="col-md-2 border-end">
                                    <div class="profile-wrapper">
                                        <div class="profile-container">
                                            <div class="badge-wrapper">
                                                <div class="z-badge">
                                                    <i class="fas fa-award"></i> Z53
                                                </div>
                                            </div>
                                            <div class="image-wrapper">
                                                <img 
                                                    :src="profileImage || '/img/avatar2.jpg'" 
                                                    alt="Profile Picture"
                                                    class="profile-image"
                                                >
                                            </div>
                                            <h5 class="profile-name"><PERSON> T<PERSON>pa</h5>
                                            <span class="status-badge">Active Customer</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Customer Details -->
                                <div class="col-md-10">
                                    <div class="row g-0">
                                        <!-- Customer Information -->
                                        <div class="col-md-6 border-end">
                                            <div class="p-4">
                                                <div class="customer-info">
                                                    <div class="customer-info-item mb-3">
                                                        <span class="info-label">Customer Registration No :</span>
                                                        <span class="info-value">+977 98XXXXXXXX</span>
                                                    </div>
                                                    <div class="customer-info-item mb-3">
                                                        <span class="info-label">Customer Level :</span>
                                                        <span class="info-value">Bronze</span>
                                                    </div>
                                                    <div class="customer-info-item mb-3">
                                                        <span class="info-label">Customer Email :</span>
                                                        <span class="info-value"><EMAIL></span>
                                                    </div>
                                                    <div class="customer-info-item mb-3">
                                                        <span class="info-label">Date Of Birth (D.O.B.) :</span>
                                                        <span class="info-value">XX-XX-XXXX</span>
                                                    </div>
                                                    <div class="customer-info-item mb-3">
                                                        <span class="info-label">Gender :</span>
                                                        <span class="info-value">Male</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Address Information -->
                                        <div class="col-md-6">
                                            <div class="p-4">
                                                <h6 class="mb-3">Address</h6>
                                                <div class="customer-info">
                                                    <div class="customer-info-item mb-3">
                                                        <span class="info-label">Province :</span>
                                                        <span class="info-value">Gandaki Province</span>
                                                    </div>
                                                    <div class="customer-info-item mb-3">
                                                        <span class="info-label">District :</span>
                                                        <span class="info-value">Kaski</span>
                                                    </div>
                                                    <div class="customer-info-item mb-3">
                                                        <span class="info-label">Street :</span>
                                                        <span class="info-value">Pokhara Metropolitan City-30,
                                                            Dhungepatan</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Ticketing and Complaint Details -->
                    <div class="row">
                        <!-- Ticketing Details -->
                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h6 class="mb-0">Ticketing Details</h6>
                                </div>
                                <div class="card-body">
                                    <div class="customer-info">
                                        <div class="customer-info-item mb-3">
                                            <span class="info-label">Total Booked Tickets :</span>
                                            <span class="info-value">47</span>
                                        </div>
                                        <div class="customer-info-item mb-3">
                                            <span class="info-label">Average Travel Interval :</span>
                                            <span class="info-value">3 Months</span>
                                        </div>
                                        <div class="customer-info-item mb-3">
                                            <span class="info-label">Frequently Travelled Route :</span>
                                            <span class="info-value">Kathmandu - Pokhara</span>
                                        </div>
                                        <div class="customer-info-item mb-3">
                                            <span class="info-label">Frequently Travelled Month :</span>
                                            <span class="info-value">February</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Complaint Details -->
                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h6 class="mb-0">Complaint Details</h6>
                                </div>
                                <div class="card-body">
                                    <div class="customer-info">
                                        <div class="customer-info-item mb-3">
                                            <span class="info-label">Total Complaints :</span>
                                            <span class="info-value">10</span>
                                        </div>
                                        <div class="customer-info-item mb-3">
                                            <span class="info-label">Solved Complaints :</span>
                                            <span class="info-value">7</span>
                                        </div>
                                        <div class="customer-info-item mb-3">
                                            <span class="info-label">Unsolved Complaints :</span>
                                            <span class="info-value">2</span>
                                        </div>
                                        <div class="customer-info-item mb-3">
                                            <span class="info-label">Inauthentic Complaints :</span>
                                            <span class="info-value">1</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Last Call Detail Section -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card mb-4">
                                <div class="card-body p-0">
                                    <div class="row g-0">
                                        <!-- Last Call Detail -->
                                        <div class="col-md-8 border-end">
                                            <div class="p-4">
                                                <div class="call-detail-header border-bottom pb-2 mb-3">
                                                    <h6 class="mb-0">Last Call Detail</h6>
                                                </div>
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="customer-info">
                                                            <div class="customer-info-item mb-3">
                                                                <span class="info-label">Call Date :</span>
                                                                <span class="info-value">XX-XX-XXXX</span>
                                                            </div>
                                                            <div class="customer-info-item mb-3">
                                                                <span class="info-label">Call Duration :</span>
                                                                <span class="info-value">02 Min 28 Sec</span>
                                                            </div>
                                                            <div class="customer-info-item mb-3">
                                                                <span class="info-label">Call Topic :</span>
                                                                <span class="info-value">Ticket Not Confirmed</span>
                                                            </div>
                                                            <div class="customer-info-item mb-3">
                                                                <span class="info-label">Case Status :</span>
                                                                <span class="info-value">Open</span>
                                                            </div>
                                                            <div class="customer-info-item mb-3">
                                                                <span class="info-label">Call Handled By :</span>
                                                                <span class="info-value">Ashis Thapa</span>
                                                            </div>
                                                            <div class="customer-info-item mb-3">
                                                                <span class="info-label">Case Forwarded To :</span>
                                                                <span class="info-value">Deepak Thapa</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="customer-info">
                                                            <div class="customer-info-item mb-3">
                                                                <span class="info-label">Call Time :</span>
                                                                <span class="info-value">07:43 AM</span>
                                                            </div>
                                                            <div class="customer-info-item mb-3">
                                                                <span class="info-label">Call Reason :</span>
                                                                <span class="info-value">Complain</span>
                                                            </div>
                                                            <div class="customer-info-item mb-3">
                                                                <span class="info-label">Call Description :</span>
                                                                <span class="info-value">After booking ticket and continuing
                                                                    to payment gateway, error message is shown and page is
                                                                    redirected to trip page.</span>
                                                            </div>
                                                        </div>
                                                        <div class="mt-4">
                                                            <button class="btn btn-sm btn-outline-primary">Take
                                                                Action</button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Actions -->
                                        <div class="col-md-4">
                                            <div class="p-4">
                                                <div class="action-header border-bottom pb-2 mb-3">
                                                    <h6 class="mb-0">Actions</h6>
                                                </div>
                                                <div class="action-buttons">
                                                    <div class="row g-2 mb-2">
                                                        <div class="col-6">
                                                            <button
                                                                class="btn btn-outline-secondary w-100">Complain</button>
                                                        </div>
                                                        <div class="col-6">
                                                            <button class="btn btn-outline-secondary w-100">Inquiry</button>
                                                        </div>
                                                    </div>
                                                    <div class="row g-2">
                                                        <div class="col-6">
                                                            <button class="btn btn-outline-secondary w-100">Action</button>
                                                        </div>
                                                        <div class="col-6">
                                                            <button class="btn btn-outline-secondary w-100">General</button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Ticket Listing Section -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card mb-4">
                                <div class="card-body">
                                    <!-- Ticket Type Tabs -->
                                    <div class="ticket-tabs mb-3">
                                        <ul class="nav nav-tabs" id="ticketTabs" role="tablist">
                                            <li class="nav-item" role="presentation">
                                                <button class="nav-link active" id="bus-ticket-tab" data-bs-toggle="tab"
                                                    data-bs-target="#bus-ticket" type="button" role="tab"
                                                    aria-controls="bus-ticket" aria-selected="true">Bus Ticket</button>
                                            </li>
                                            <li class="nav-item" role="presentation">
                                                <button class="nav-link" id="reservation-tab" data-bs-toggle="tab"
                                                    data-bs-target="#reservation" type="button" role="tab"
                                                    aria-controls="reservation" aria-selected="false">Reservation</button>
                                            </li>
                                            <li class="nav-item" role="presentation">
                                                <button class="nav-link" id="rental-tab" data-bs-toggle="tab"
                                                    data-bs-target="#rental" type="button" role="tab" aria-controls="rental"
                                                    aria-selected="false">Rental</button>
                                            </li>
                                            <li class="nav-item" role="presentation">
                                                <button class="nav-link" id="flight-ticket-tab" data-bs-toggle="tab"
                                                    data-bs-target="#flight-ticket" type="button" role="tab"
                                                    aria-controls="flight-ticket" aria-selected="false">Flight
                                                    Ticket</button>
                                            </li>
                                        </ul>
                                    </div>

                                    <!-- Tab Content -->
                                    <div class="tab-content" id="ticketTabsContent">
                                        <!-- Bus Ticket Tab -->
                                        <div class="tab-pane fade show active" id="bus-ticket" role="tabpanel"
                                            aria-labelledby="bus-ticket-tab">
                                            <!-- Search Bar -->
                                            <div class="ticket-search mb-3">
                                                <div class="row">
                                                    <div class="col-md-4">
                                                        <div class="input-group">
                                                            <input type="text" class="form-control" placeholder="Search...">
                                                            <button class="btn btn-outline-secondary"
                                                                type="button">Search</button>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-8 text-end">
                                                        <button class="btn btn-sm btn-outline-secondary me-2">
                                                            <i class="fa fa-print"></i> Print
                                                        </button>
                                                        <button class="btn btn-sm btn-outline-secondary">
                                                            <i class="fa fa-download"></i> Export
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Bus Ticket Table -->
                                            <div class="table-responsive">
                                                <table class="table table-bordered table-hover ticket-table">
                                                    <thead>
                                                        <tr>
                                                            <th style="width: 30px;"><input type="checkbox"
                                                                    class="form-check-input"></th>
                                                            <th>Ticket Id</th>
                                                            <th>Route</th>
                                                            <th>Seats</th>
                                                            <th>Ticket Price</th>
                                                            <th>Trip Date</th>
                                                            <th>Trip Time</th>
                                                            <th>Ticket Status</th>
                                                            <th>Operator</th>
                                                            <th>Bus Name</th>
                                                            <th>Bus No</th>
                                                            <th>Staff No.</th>
                                                            <th>Trip Status</th>
                                                            <th>Feedback</th>
                                                            <th>Complaint</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <tr>
                                                            <td><input type="checkbox" class="form-check-input"></td>
                                                            <td>Ticket Id</td>
                                                            <td>Kathmandu - Pokhara</td>
                                                            <td>A6</td>
                                                            <td>Rs. 1,600</td>
                                                            <td>XX-XX-XXXX</td>
                                                            <td>XX:XX am</td>
                                                            <td>Successful</td>
                                                            <td>Baba Adventure</td>
                                                            <td>Baba VIP 2X1 Sofa</td>
                                                            <td>Ga 1 Kha 4567</td>
                                                            <td>+98XXXXXXXX</td>
                                                            <td>Completed</td>
                                                            <td>Given</td>
                                                            <td>No</td>
                                                        </tr>
                                                        <tr>
                                                            <td><input type="checkbox" class="form-check-input"></td>
                                                            <td>0778346-B</td>
                                                            <td>Pokhara - Kathmandu</td>
                                                            <td>B3</td>
                                                            <td>Rs. 1,200</td>
                                                            <td>XX-XX-XXXX</td>
                                                            <td>XX:XX pm</td>
                                                            <td>Successful</td>
                                                            <td>Baba Adventure</td>
                                                            <td>Baba VIP 2X1 Sofa</td>
                                                            <td>Ga 1 Kha 4567</td>
                                                            <td>+98XXXXXXXX</td>
                                                            <td>Completed</td>
                                                            <td>Given</td>
                                                            <td>No</td>
                                                        </tr>
                                                        <tr>
                                                            <td><input type="checkbox" class="form-check-input"></td>
                                                            <td>0876587-B</td>
                                                            <td>Kathmandu - Chitwan</td>
                                                            <td>A1, A2, A3, A4</td>
                                                            <td>Rs. 2,400</td>
                                                            <td>XX-XX-XXXX</td>
                                                            <td>XX:XX pm</td>
                                                            <td>Successful</td>
                                                            <td>Chitwan Safari</td>
                                                            <td>Chitwan Deluxe</td>
                                                            <td>Na 2 Kha 5476</td>
                                                            <td>+98XXXXXXXX</td>
                                                            <td>Completed</td>
                                                            <td>Not Given</td>
                                                            <td>No</td>
                                                        </tr>
                                                        <tr>
                                                            <td><input type="checkbox" class="form-check-input"></td>
                                                            <td>0578634-B</td>
                                                            <td>Chitwan - Kathmandu</td>
                                                            <td>B6, B7, B8</td>
                                                            <td>Rs. 1,400</td>
                                                            <td>XX-XX-XXXX</td>
                                                            <td>XX:XX am</td>
                                                            <td>Successful</td>
                                                            <td>Chitwan Safari</td>
                                                            <td>Chitwan Micro</td>
                                                            <td>Na 4 Kha 8794</td>
                                                            <td>+98XXXXXXXX</td>
                                                            <td>Completed</td>
                                                            <td>Given</td>
                                                            <td>Yes</td>
                                                        </tr>
                                                        <tr>
                                                            <td><input type="checkbox" class="form-check-input"></td>
                                                            <td>0467367-B</td>
                                                            <td>Kathmandu - Pokhara</td>
                                                            <td>B10, B11</td>
                                                            <td>Rs. 3,200</td>
                                                            <td>XX-XX-XXXX</td>
                                                            <td>XX:XX am</td>
                                                            <td>Successful</td>
                                                            <td>Shiva Jagadamba Travels</td>
                                                            <td>VIP Sofa 2X1</td>
                                                            <td>Ga 3 Kha 5438</td>
                                                            <td>+98XXXXXXXX</td>
                                                            <td>Completed</td>
                                                            <td>Given</td>
                                                            <td>No</td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>

                                        <!-- Reservation Tab -->
                                        <div class="tab-pane fade" id="reservation" role="tabpanel"
                                            aria-labelledby="reservation-tab">
                                            <div class="ticket-search mb-3">
                                                <div class="row">
                                                    <div class="col-md-4">
                                                        <div class="input-group">
                                                            <input type="text" class="form-control"
                                                                placeholder="Search reservations...">
                                                            <button class="btn btn-outline-secondary"
                                                                type="button">Search</button>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-8 text-end">
                                                        <button class="btn btn-sm btn-outline-secondary me-2">
                                                            <i class="fa fa-print"></i> Print
                                                        </button>
                                                        <button class="btn btn-sm btn-outline-secondary">
                                                            <i class="fa fa-download"></i> Export
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="alert alert-info">
                                                No reservation records found for this customer.
                                            </div>
                                        </div>

                                        <!-- Rental Tab -->
                                        <div class="tab-pane fade" id="rental" role="tabpanel" aria-labelledby="rental-tab">
                                            <div class="ticket-search mb-3">
                                                <div class="row">
                                                    <div class="col-md-4">
                                                        <div class="input-group">
                                                            <input type="text" class="form-control"
                                                                placeholder="Search rentals...">
                                                            <button class="btn btn-outline-secondary"
                                                                type="button">Search</button>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-8 text-end">
                                                        <button class="btn btn-sm btn-outline-secondary me-2">
                                                            <i class="fa fa-print"></i> Print
                                                        </button>
                                                        <button class="btn btn-sm btn-outline-secondary">
                                                            <i class="fa fa-download"></i> Export
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="alert alert-info">
                                                No rental records found for this customer.
                                            </div>
                                        </div>

                                        <!-- Flight Ticket Tab -->
                                        <div class="tab-pane fade" id="flight-ticket" role="tabpanel"
                                            aria-labelledby="flight-ticket-tab">
                                            <div class="ticket-search mb-3">
                                                <div class="row">
                                                    <div class="col-md-4">
                                                        <div class="input-group">
                                                            <input type="text" class="form-control"
                                                                placeholder="Search flight tickets...">
                                                            <button class="btn btn-outline-secondary"
                                                                type="button">Search</button>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-8 text-end">
                                                        <button class="btn btn-sm btn-outline-secondary me-2">
                                                            <i class="fa fa-print"></i> Print
                                                        </button>
                                                        <button class="btn btn-sm btn-outline-secondary">
                                                            <i class="fa fa-download"></i> Export
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="alert alert-info">
                                                No flight ticket records found for this customer.
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Pagination -->
                                    <div class="d-flex justify-content-between align-items-center mt-3">
                                        <div class="pagination-info">
                                            Showing 1 to 5 of 12 entries
                                        </div>
                                        <nav aria-label="Page navigation">
                                            <ul class="pagination pagination-sm">
                                                <li class="page-item">
                                                    <a class="page-link" href="#" aria-label="First">
                                                        <span aria-hidden="true">«</span>
                                                    </a>
                                                </li>
                                                <li class="page-item">
                                                    <a class="page-link" href="#" aria-label="Previous">
                                                        <span aria-hidden="true">‹</span>
                                                    </a>
                                                </li>
                                                <li class="page-item active"><a class="page-link" href="#">1</a></li>
                                                <li class="page-item"><a class="page-link" href="#">2</a></li>
                                                <li class="page-item"><a class="page-link" href="#">3</a></li>
                                                <li class="page-item"><a class="page-link" href="#">4</a></li>
                                                <li class="page-item"><a class="page-link" href="#">5</a></li>
                                                <li class="page-item">
                                                    <a class="page-link" href="#" aria-label="Next">
                                                        <span aria-hidden="true">›</span>
                                                    </a>
                                                </li>
                                                <li class="page-item">
                                                    <a class="page-link" href="#" aria-label="Last">
                                                        <span aria-hidden="true">»</span>
                                                    </a>
                                                </li>
                                            </ul>
                                        </nav>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </template>

    <script setup>
import { ref } from 'vue'

const profileImage = ref(null);
    
</script>
<style scoped>
/* @import url('https://unpkg.com/tailwindcss@^2/dist/tailwind.min.css'); */

.profile-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    width: 100%;
}

.profile-container {
    width: 180px;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.profile-container:hover {
    transform: translateY(-5px);
}

.badge-wrapper {
    position: absolute;
    top: -5px;
    width: 100%;
    display: flex;
    justify-content: center;
    z-index: 2;
}

.z-badge {
    background: #FFB800;
    color: #000;
    padding: 4px 12px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 14px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.profile-container:hover .z-badge {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.image-wrapper {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    padding: 3px;
    background: linear-gradient(45deg, #6366f1, #8b5cf6);
    margin-top: 20px;
    position: relative;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.profile-container:hover .image-wrapper {
    transform: scale(1.05);
    box-shadow: 0 0 20px rgba(99, 102, 241, 0.4);
}

.profile-image {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid white;
    transition: transform 0.3s ease;
}

.profile-name {
    margin-top: 15px;
    margin-bottom: 8px;
    font-size: 16px;
    font-weight: 600;
    color: #2d3748;
    text-align: center;
    transition: color 0.3s ease;
}

.profile-container:hover .profile-name {
    color: #6366f1;
}

.status-badge {
    background: #E2E8F0;
    color: #4A5568;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.profile-container:hover .status-badge {
    background: #6366f1;
    color: white;
    transform: translateY(2px);
}

/* Optional: Add a subtle gradient animation on hover */
@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.profile-container:hover .image-wrapper {
    background: linear-gradient(45deg, #6366f1, #8b5cf6, #6366f1);
    background-size: 200% 200%;
    animation: gradientShift 3s ease infinite;
}

.customer-info-item {
    display: flex;
    flex-direction: column;
}

.info-label {
    color: #6c757d;
    font-size: 14px;
    margin-bottom: 4px;
}

.info-value {
    font-weight: 500;
    font-size: 15px;
}

@media (min-width: 768px) {
    .customer-info-item {
        flex-direction: row;
    }

    .info-label {
        min-width: 200px;
        margin-bottom: 0;
    }
}

/* Ticket table styles */
.ticket-table {
    font-size: 14px;
}

.ticket-table th {
    background-color: #f8f9fa;
    font-weight: 500;
    vertical-align: middle;
}

.ticket-table td {
    vertical-align: middle;
}

.ticket-tabs .nav-link {
    font-size: 15px;
    font-weight: 500;
    color: #6c757d;
    padding: 10px 20px;
}

.ticket-tabs .nav-link.active {
    font-weight: 600;
    color: #000;
}

.pagination-info {
    font-size: 14px;
    color: #6c757d;
}
</style>
