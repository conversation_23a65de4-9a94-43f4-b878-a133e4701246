import {
  createRouter,
  createWebHistory,
} from 'vue-router'

import FrontendLayout from '@/layouts/FrontendLayout.vue'
import MainLayout from '@/layouts/MainLayout.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    //Landing Page Routes
    {
      path: "/",
      component: FrontendLayout,
      children: [
        {
          path: "",
          name: "LandingPage",
          component: () => import("@/views/Frontend/LandingPage.vue"),
        },
        {
          path: "provision-executive-director",
          name: "ProvisionExecutiveDirector",
          component: () =>
            import("@/components/frontend/Navigation/ProvisionExecutiveDirector.vue"),
        },
        {
          path: 'miscellaneous',
          name: 'miscellaneous',
          component: () =>
            import("@/components/frontend/Navigation/Miscellaneous.vue"),
        },
        {
          path: 'publications/annual-reports',
          name: 'annual-reports',
          component: () =>
            import("@/components/frontend/Navigation/ReportList.vue"),
          props: { type: 'annual', title: 'Annual Reports', subtitle: 'Access and download our Annual reports' }
        },
        {
          path: 'publications/audit-reports',
          name: 'audit-reports',
          component: () =>
            import("@/components/frontend/Navigation/ReportList.vue"),
          props: { type: 'audit', title: 'Audit Reports', subtitle: 'Access and download our audit reports' }
        },
        {
          path: 'publications/water-quality-test-report',
          name: 'water-quality-test-report',
          component: () =>
            import("@/components/frontend/Navigation/ReportList.vue"),
          props: { type: 'waterQualityTestReport', title: 'Water Quality Test Reports', subtitle: 'Access and download our water quality test reports' }
        },
        {
          path: 'publications/downloads',
          name: 'downloads',
          component: () =>
            import("@/components/frontend/Navigation/ReportList.vue"),
          props: { type: 'downloads', title: 'Download Test Reports', subtitle: 'Access and download our  reports' }

        },
        //Notices
        {
          path: 'notices/tender-notices',
          name: 'tender-notices',
          component: () =>
            import("@/components/frontend/Navigation/ReportList.vue"),
          props: { type: 'tender', title: 'Tender Notice Reports', subtitle: 'Access and download our Tender reports' }
        },
        {
          path: 'notices/vacancy',
          name: 'vacancy',
          component: () =>
            import("@/components/frontend/Navigation/ReportList.vue"),
          props: { type: 'vacancy', title: 'Vacancy Notices', subtitle: 'Access and download our Vacancy notices' }
        },
        {
          path: 'notices/tariff-and-others',
          name: 'tariff-and-others',
          component: () =>
            import("@/components/frontend/Navigation/ReportList.vue"),
          props: { type: 'tariff', title: 'Tariff and Other Notices', subtitle: 'Access and download our Tariff and other notices' }
        },
        {
          path: 'notices/consumer-notice',
          name: 'consumer-notice',
          component: () =>
            import("@/components/frontend/Navigation/ReportList.vue"),
          props: { type: 'consumer', title: 'Consumer Notices', subtitle: 'Access and download our Consumer notices' }
        },
        //Career
        {
          path: 'career',
          name: 'career',
          component: () =>
            import("@/components/frontend/Navigation/ReportList.vue"),
          props: { type: 'career', title: 'Consumer Notices', subtitle: 'Access and download our Consumer notices' }

        },
        //Gallery
        {
          path: 'gallery',
          name: 'gallery',
          component: () =>
            import("@/components/frontend/Navigation/Gallery.vue"),
        },
        {
          path: 'contact',
          name: 'contact',
          component: () =>
            import("@/components/frontend/Navigation/Contact.vue"),
        },
        {
          path: 'categories',
          name: 'categories',
          component: () =>
            import("@/components/frontend/Navigation/Categories.vue"),
        },
        {
          path: 'members',
          name: 'members',
          component: () =>
            import("@/components/frontend/Navigation/Members.vue"),
        },
        // Add more frontend routes here as needed
      ],
    },
    // Import admin routes
    {
      path: "/dashboard",
      component: MainLayout,
      children: [
        {
          path: "",
          name: "home",
          component: () => import("@/views/DashboardContent.vue"),
        },
        {
          path: "about",
          name: "about",
          component: () => import("@/views/AboutContent.vue"),
        },
        {
          path: "customer-details",
          name: "customer-details",
          component: () => import("@/views/CustomerDetails.vue"),
        },
        {
          path: "admissions-table",
          name: "admissions-table",
          component: () => import("@/views/AdmissionsTableExample.vue"),
        },
      ],
    },
    {
      path: "/login",
      component: () => import("@/views/LoginView.vue"),
    },
  ],
});

// router.beforeEach((to, from, next) => {
//   const authStore = useAuthStore();
//   const isAdminRoute = to.meta.isAdmin;

//   if (isAdminRoute && !authStore.isAuthenticated) {
//     next("/login");
//   } else if (authStore.isAuthenticated && to.path === "/login") {
//     next("/");
//   } else {
//     next();
//   }
// });

export default router;
