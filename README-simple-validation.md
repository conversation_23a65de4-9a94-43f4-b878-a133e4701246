# Simple Vue Validation

![Simple Vue Validation](https://via.placeholder.com/1200x300/4CAF50/FFFFFF?text=Simple+Vue+Validation)

A unique, intuitive validation system for Vue 3 applications with minimal setup and beautiful UI feedback.

## Table of Contents

- [Features](#features)
- [Quick Start](#quick-start)
- [How to Use](#how-to-use)
- [Available Components](#available-components)
- [Validation Rules](#available-validation-rules)
- [Form Methods](#form-methods)
- [Field Properties](#field-properties)
- [Advanced Usage](#advanced-usage)
- [Why Use This System](#why-use-this-validation-system)
- [Example](#example)

## Features

- ✅ Simple, intuitive API
- ✅ Minimal boilerplate code
- ✅ Great UI feedback
- ✅ Globally available without explicit imports
- ✅ Flexible validation rules
- ✅ Form-level and field-level validation

## Quick Start

```javascript
// 1. Import the validation system
import { useValidation, rules } from '@/plugins/vueValidate'

// 2. Create a form
const form = useValidation({ name: '', email: '' })

// 3. Register fields with validation rules
const nameField = form.register('name', [rules.required()])
const emailField = form.register('email', [rules.required(), rules.email()])

// 4. Use in your template
<form @submit.prevent="form.handleSubmit(onSubmit)">
  <VInput :field="nameField" label="Name" />
  <VInput :field="emailField" label="Email" type="email" />
  <button type="submit">Submit</button>
</form>

// 5. Handle submission
const onSubmit = (values) => {
  console.log('Form submitted:', values)
}
```

## How to Use

### 1. Create a Form

```javascript
import { useValidation, rules } from '@/plugins/vueValidate'

// Create a form with initial values
const form = useValidation({
  name: '',
  email: ''
})
```

### 2. Register Fields with Validation Rules

```javascript
// Register fields with validation rules
const nameField = form.register('name', [
  rules.required('Please enter your name'),
  rules.minLength(3, 'Name must be at least 3 characters')
])

const emailField = form.register('email', [
  rules.required('Please enter your email'),
  rules.email('Please enter a valid email address')
])
```

### 3. Create Your Form Template

```vue
<form @submit.prevent="form.handleSubmit(onSubmit)">
  <VInput
    :field="nameField"
    label="Name"
    placeholder="Enter your name"
  />

  <VInput
    :field="emailField"
    label="Email"
    type="email"
    placeholder="Enter your email"
  />

  <button type="submit" class="btn btn-primary">Submit</button>
</form>
```

### 4. Handle Form Submission

```javascript
// This function will only run if validation passes
const onSubmit = (values) => {
  console.log('Form submitted:', values)
}
```

## Available Components

### VInput

```vue
<VInput
  :field="nameField"
  label="Name"
  placeholder="Enter your name"
  type="text"
  id="name"
/>
```

### VSelect

```vue
<VSelect
  :field="countryField"
  label="Country"
  :options="countries"
  placeholder="Select your country"
  id="country"
/>
```

### VCheckbox

```vue
<VCheckbox
  :field="termsField"
  label="I accept the terms and conditions"
  id="terms"
/>
```

### VTextarea

```vue
<VTextarea
  :field="messageField"
  label="Message"
  placeholder="Enter your message"
  id="message"
  rows="5"
/>
```

## Available Validation Rules

| Rule | Description | Example |
|------|-------------|--------|
| `required` | Field cannot be empty | `rules.required('This field is required')` |
| `email` | Must be a valid email | `rules.email('Please enter a valid email')` |
| `minLength` | Minimum string length | `rules.minLength(3, 'Too short')` |
| `maxLength` | Maximum string length | `rules.maxLength(100, 'Too long')` |
| `min` | Minimum numeric value | `rules.min(18, 'Must be at least 18')` |
| `max` | Maximum numeric value | `rules.max(100, 'Must be less than 100')` |
| `pattern` | Match a regex pattern | `rules.pattern(/^\d{5}$/, 'Must be 5 digits')` |
| `match` | Match another field | `rules.match('password', 'Must match password')` |
| `custom` | Custom validation logic | `rules.custom(value => value === true, 'Required')` |

### Required

```javascript
rules.required('This field is required')
```

### Email

```javascript
rules.email('Please enter a valid email address')
```

### Min Length

```javascript
rules.minLength(3, 'Must be at least 3 characters')
```

### Max Length

```javascript
rules.maxLength(100, 'Must be less than 100 characters')
```

### Min Value

```javascript
rules.min(18, 'Must be at least 18')
```

### Max Value

```javascript
rules.max(100, 'Must be less than 100')
```

### Pattern

```javascript
rules.pattern(/^\d{5}$/, 'Must be 5 digits')
```

### Match

```javascript
rules.match('password', 'Must match password')
```

### Custom

```javascript
rules.custom(value => value === true, 'You must accept the terms')
```

## Form Methods

### validate

Validates all fields and returns true if all are valid.

```javascript
const isValid = form.validate()
```

### reset

Resets the form to its initial values and clears all errors.

```javascript
form.reset()
```

### handleSubmit

Creates a submit handler that validates the form and calls your function if valid.

```javascript
const handleSubmit = form.handleSubmit(values => {
  // Process form submission
})
```

## Field Properties

Each registered field has the following properties:

### value

A reactive reference to the field value.

```javascript
nameField.value // Get the current value
nameField.value = 'New value' // Set a new value
```

### errors

A computed property that returns an array of error messages.

```javascript
nameField.errors.value // ['This field is required']
```

### touched

A computed property that indicates if the field has been touched.

```javascript
nameField.touched.value // true or false
```

### hasError

A computed property that indicates if the field has errors and has been touched.

```javascript
nameField.hasError.value // true or false
```

### isValid

A computed property that indicates if the field is valid.

```javascript
nameField.isValid.value // true or false
```

### setTouched

A method to mark the field as touched and trigger validation.

```javascript
nameField.setTouched()
```

## Advanced Usage

### Conditional Validation

```javascript
import { ref, computed } from 'vue'
import { useValidation, rules } from '@/plugins/vueValidate'

const form = useValidation({
  contactMethod: 'email',
  email: '',
  phone: ''
})

const contactMethodField = form.register('contactMethod', [])

// Conditionally validate email or phone based on contact method
const emailField = form.register('email', computed(() => {
  return contactMethodField.value.value === 'email'
    ? [rules.required(), rules.email()]
    : []
}))

const phoneField = form.register('phone', computed(() => {
  return contactMethodField.value.value === 'phone'
    ? [rules.required(), rules.pattern(/^\d{10}$/, 'Must be 10 digits')]
    : []
}))
```

### Async Validation

```javascript
const usernameField = form.register('username', [
  rules.required(),
  rules.minLength(3),
  rules.custom(async (value) => {
    if (!value) return true

    try {
      const response = await fetch(`/api/check-username?username=${value}`)
      const data = await response.json()
      return data.available || 'Username already taken'
    } catch (error) {
      return 'Error checking username'
    }
  })
])
```

### Creating Custom Form Components

```vue
<template>
  <div class="custom-input" :class="{ 'has-error': field.hasError.value }">
    <label v-if="label">{{ label }}</label>
    <div class="input-wrapper">
      <span v-if="icon" class="icon">{{ icon }}</span>
      <input
        :type="type"
        :placeholder="placeholder"
        v-model="field.value"
        @blur="field.setTouched"
      />
    </div>
    <div v-if="field.hasError.value" class="error-message">
      {{ field.errors.value[0] }}
    </div>
  </div>
</template>

<script setup>
defineProps({
  field: Object,
  label: String,
  type: { type: String, default: 'text' },
  placeholder: String,
  icon: String
})
</script>
```

## Why Use This Validation System?

1. **Simplicity**: The API is designed to be intuitive and easy to use.
2. **Flexibility**: You can use the built-in components or create your own.
3. **Performance**: The validation system is optimized for performance.
4. **Integration**: Works seamlessly with Vue 3 and the Composition API.
5. **Customization**: Easy to customize with your own validation rules and error messages.
6. **Minimal Setup**: Get started with just a few lines of code.
7. **Great UX**: Beautiful error messages and visual feedback.
8. **Type Safety**: Works well with TypeScript for better developer experience.
9. **Reactive**: Fully reactive with Vue's reactivity system.
10. **Lightweight**: Small footprint with no external dependencies.

## Example

See the complete example at `/simple-validation` in your application.

---

## Need Help?

If you have any questions or need help with the validation system, please refer to the example implementation or contact the development team.

---

## License

MIT

---

Happy coding! 🚀
