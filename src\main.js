import './assets/style.css'
import './assets/sidebar.css'
import './assets/toastr.css'
import './assets/toastr.js'
import './assets/validation.css'
import './assets/vue-validate.css'
import './assets/simple-validator.css'
import './assets/code-blocks.css'
import './assets/code-blocks.js'

import { createApp } from 'vue'

import { createPinia } from 'pinia'

import App from './App.vue'
// Import validation systems
import ValidationComponents from './components/FormValidation'
import SimpleValidationComponents from './components/SimpleValidation'
import VueDataTable from './plugins/vue-data-table'
import VueValidate from './plugins/vueValidate'
import router from './router'

const app = createApp(App)

app.use(createPinia())
app.use(router)
app.use(ValidationComponents)
app.use(SimpleValidationComponents)
app.use(VueValidate)
app.use(VueDataTable)

app.mount('#app')


