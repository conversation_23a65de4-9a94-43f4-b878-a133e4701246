<template>
  <section class="function-component py-12 bg-white">
    <div class="container mx-auto px-4">
      <!-- Section Title -->
      <div class="mb-10 text-center">
        <h2
          class="text-3xl md:text-4xl font-bold text-blue-800 mb-2 relative inline-block"
        >
          Introduction
          <span
            class="absolute bottom-0 left-0 w-full h-1 bg-blue-600 transform scale-x-0 transition-transform duration-500 intro-underline"
          ></span>
        </h2>
        <!-- <p class="text-gray-600 max-w-3xl mx-auto">
          Learn about the Bharatpur Water Supply Management Board and its functions
        </p> -->
      </div>

      <!-- Content with Image and Text -->
      <div class="flex flex-col md:flex-row items-start gap-8 md:gap-12">
        <!-- Image with Effects -->
        <div
          class="w-full md:w-1/2 overflow-hidden rounded-lg shadow-xl transform transition-all duration-500 hover:scale-[1.02] intro-image"
        >
          <img
        :src="image"
        alt="BWSMB Office Building"
        class="w-full h-auto object-cover transition-transform duration-7000 hover:scale-110"
          />
        </div>

        <!-- Text Content with Modern Typography -->
        <div class="w-full md:w-1/2 intro-text flex flex-col justify-start">
          <div class="prose prose-lg max-w-none">
        <div v-if="introData && introData.length > 0">
          <div
           v-html="introData[0].message"
           ></div>
        </div>
        <div v-else>No Data Found</div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import {
  onMounted,
  ref,
} from 'vue'

import api from '@/api/api'
import { ORG_ID } from '@/api/endpoint'

const introData = ref(null);
const image = ref(null);
onMounted(async () => {
  try {
    const response = await api.get(`/customer/aboutUs/${ORG_ID}`);
    if (response.data.status === 1) {
      introData.value = response.data.aboutUsDetails;
      image.value = introData.value[0].image;
    }
  } catch (error) {}
  // Add animation when component is mounted
  setTimeout(() => {
    const introUnderline = document.querySelector(".intro-underline");
    const introImage = document.querySelector(".intro-image");
    const introText = document.querySelector(".intro-text");

    if (introUnderline) introUnderline.classList.add("scale-x-100");
    if (introImage) introImage.classList.add("animate-fade-in");
    if (introText) introText.classList.add("animate-slide-in");
  }, 300);
});
</script>

<style scoped>
.function-component {
  position: relative;
  overflow: hidden;
}

.function-component::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%232563eb' fill-opacity='0.03' fill-rule='evenodd'/%3E%3C/svg%3E");
  opacity: 0.5;
  z-index: 0;
}

.scale-x-100 {
  transform: scaleX(1) !important;
}

.animate-fade-in {
  animation: fadeIn 1s ease forwards;
}

.animate-slide-in {
  animation: slideIn 1s ease forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .prose {
    font-size: 0.95rem;
  }
}
</style>
