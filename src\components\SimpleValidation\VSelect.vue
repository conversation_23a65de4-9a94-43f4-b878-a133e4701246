<script setup>
import {
  computed,
  ref,
} from 'vue'

const props = defineProps({
  field: {
    type: Object,
    required: true
  },
  label: {
    type: String,
    default: ''
  },
  options: {
    type: Array,
    required: true
  },
  placeholder: {
    type: String,
    default: 'Select an option'
  },
  id: {
    type: String,
    default: ''
  },
  valueKey: {
    type: String,
    default: 'value'
  },
  labelKey: {
    type: String,
    default: 'label'
  },
  validateOnChange: {
    type: Boolean,
    default: false
  }
})

// Only show errors after the user has interacted with the field and moved away
const localTouched = ref(false)

const handleBlur = () => {
  localTouched.value = true
  props.field.setTouched()
}

const handleChange = () => {
  if (props.validateOnChange) {
    localTouched.value = true
    props.field.setTouched()
  }
}

const classes = computed(() => ({
  'v-form-group': true,
  'has-error': localTouched.value && props.field.hasError.value
}))
</script>

<template>
  <div :class="classes">
    <label v-if="label" :for="id">{{ label }}</label>
    <select :id="id" v-model="field.value" @blur="handleBlur" @change="handleChange">
      <option value="" disabled>{{ placeholder }}</option>
      <option v-for="option in options" :key="typeof option === 'object' ? option[valueKey] : option"
        :value="typeof option === 'object' ? option[valueKey] : option">
        {{ typeof option === 'object' ? option[labelKey] : option }}
      </option>
    </select>
    <div v-if="localTouched && field.hasError.value" class="error-message">
      {{ field.errors.value[0] }}
    </div>
  </div>
</template>
