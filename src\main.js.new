// Base styles
import './assets/base.css'
import './assets/main.css'

// Vendor CSS
import '/assets/vendor_assets/css/apexcharts.min.css'
import '/assets/vendor_assets/css/datepicker.min.css'
import '/assets/vendor_assets/css/line-awesome.min.css'
import '/assets/vendor_assets/css/nouislider.min.css'
import '/assets/vendor_assets/css/quill.snow.css'
import '/assets/vendor_assets/css/svgMap.min.css'

// Tailwind CSS
import '/assets/theme_assets/tailwind/tailwind.css'

// Vendor JavaScript
import '/assets/vendor_assets/js/apexcharts.min.js'
import '/assets/vendor_assets/js/datepicker-full.min.js'
import '/assets/vendor_assets/js/fslightbox.js'
import '/assets/vendor_assets/js/index.global.min.js'
import '/assets/vendor_assets/js/mixitup.min.js'
import '/assets/vendor_assets/js/moment.min.js'
import '/assets/vendor_assets/js/nouislider.min.js'
import '/assets/vendor_assets/js/quill.js'
import '/assets/vendor_assets/js/svg-pan-zoom.min.js'
import '/assets/vendor_assets/js/svgMap.min.js'
import '/assets/vendor_assets/js/tw-elements.umd.min.js'
import '/assets/vendor_assets/js/yscountdown.min.js'

// Theme JavaScript
import '/assets/theme_assets/js/main-fixed.js'

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'

const app = createApp(App)

app.use(createPinia())
app.use(router)

app.mount('#app')
