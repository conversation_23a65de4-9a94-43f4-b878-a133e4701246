<template>
  <section class="relative slider-section">
    <div class="slider-container">
      <div class="slider-item active">
        <img
          src="../../assets/img/banner1.jpg"
          alt="Staff Group Photo"
          class="w-full h-full object-cover"
        />
        <!-- <div class="absolute inset-0 flex items-center justify-start z-10">
            <div class="text-white px-16 slide-content max-w-3xl">
              <div class="overflow-hidden mb-2">
                <h2 class="text-3xl md:text-5xl font-bold slide-title">
                  Dharan Water Supply Management Board
                </h2>
              </div>
              <div class="overflow-hidden mb-6">
                <p class="text-xl md:text-2xl slide-description">
                  Providing clean and safe water to our community
                </p>
              </div>
              <button
                @click="scrollToMiddle()"
                class="mt-6 px-8 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-full transition-all duration-300 transform hover:scale-105 slide-button"
              >
                Learn More
              </button>
            </div>
          </div> -->
      </div>
      <div class="slider-item">
        <img
          src="../../assets/img/banner6.jpg"
          alt="Water Treatment Facility"
          class="w-full h-full object-cover"
        />
        <!-- <div class="absolute inset-0 flex items-center justify-start z-10">
            <div class="text-white px-16 slide-content max-w-3xl">
              <div class="overflow-hidden mb-2">
                <h2 class="text-3xl md:text-5xl font-bold slide-title">
                  Modern Water Treatment
                </h2>
              </div>
              <div class="overflow-hidden mb-6">
                <p class="text-xl md:text-2xl slide-description">
                  Using advanced technology for water purification
                </p>
              </div>
              <router-link
                to="/notices/vacancy"
                class="mt-6 px-8 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-full transition-all duration-300 transform hover:scale-105 slide-button"
              >
                Our Notices
              </router-link>
            </div>
          </div> -->
      </div>
      <div class="slider-item">
        <img
          src="../../assets/img/banner7.jpg"
          alt="Community Service"
          class="w-full h-full object-cover"
        />
        <!-- <div class="absolute inset-0 flex items-center justify-start z-10">
            <div class="text-white px-16 slide-content max-w-3xl">
              <div class="overflow-hidden mb-2">
                <h2 class="text-3xl md:text-5xl font-bold slide-title">
                  Serving Our Community
                </h2>
              </div>
              <div class="overflow-hidden mb-6">
                <p class="text-xl md:text-2xl slide-description">
                  Committed to excellence in water management
                </p>
              </div>
              <button
                @click="scrollToMembers()"
                class="mt-6 px-8 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-full transition-all duration-300 transform hover:scale-105 slide-button"
              >
                Our Members
              </button>
            </div>
          </div> -->
      </div>
    </div>

    <!-- Slider Navigation Arrows -->
    <button class="slider-arrow slider-arrow-left" aria-label="Previous slide">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
        class="w-6 h-6"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M15 19l-7-7 7-7"
        />
      </svg>
    </button>
    <button class="slider-arrow slider-arrow-right" aria-label="Next slide">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
        class="w-6 h-6"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M9 5l7 7-7 7"
        />
      </svg>
    </button>

    <!-- Slider Navigation Dots -->
    <div class="absolute bottom-4 left-0 right-0 flex justify-center">
      <div class="slider-dots-container">
        <div class="slider-dot active"></div>
        <div class="slider-dot"></div>
        <div class="slider-dot"></div>
      </div>
    </div>
  </section>

  <!-- What's New Section -->
  <section class="bg-blue-900 text-white py-2">
    <div class="container mx-auto px-4 flex items-center">
      <div class="font-bold mr-4">WHAT'S NEW:</div>
      <div class="marquee overflow-hidden flex-1">
        <span
          >Welcome to the Dharan Water Supply Management Board. धरान खानेपानी व्यवस्थापन
          बोर्ड तपाईंलाई स्वागत छ ।</span
        >
      </div>
    </div>
  </section>
</template>

<script setup>
import {
  onBeforeUnmount,
  onMounted,
} from 'vue'

function scrollToMiddle() {
  const middleSection = document.querySelector(".functional-component");
  if (middleSection) {
    middleSection.scrollIntoView({ behavior: "smooth" });
  }
}

function scrollToMembers() {
  const membersSection = document.querySelector(".committee-members");
  if (membersSection) {
    membersSection.scrollIntoView({ behavior: "smooth" });
  }
}

let sliderInterval;
let currentIndex = 0;

const mockData = {
  status: 1,
  imagesList: [
    "https://watersoft.com.np/WaterTariffSystem-web/appImageUpload/viber_image_2023-02-15_16-27-02-792_1676457911903.jpg",
    "https://watersoft.com.np/WaterTariffSystem-web/appImageUpload/viber_image_2023-02-15_16-27-01-036_1676457961540.jpg",
    "https://watersoft.com.np/WaterTariffSystem-web/appImageUpload/viber_image_2023-02-15_16-27-00-984_1676458019823.jpg",
    "https://watersoft.com.np/WaterTariffSystem-web/appImageUpload/viber_image_2023-02-15_16-27-01-894_1676458035210.jpg",
    "https://watersoft.com.np/WaterTariffSystem-web/appImageUpload/IMAGE_BANNER_89_1700547339053.jpg",
    "https://watersoft.com.np/WaterTariffSystem-web/appImageUpload/IMAGE_BANNER_89_1729057770161.jpg",
    "https://watersoft.com.np/WaterTariffSystem-web/appImageUpload/IMAGE_BANNER_89_1736939845888.jpg",
    "https://watersoft.com.np/WaterTariffSystem-web/appImageUpload/IMAGE_BANNER_89_1736940222266.jpg",
  ],
};
onMounted(async () => {
  // Initialize slider functionality
  initSlider();

  //call api
  // const response = await fetch("https://api.example.com/data");
  const response = mockData;
  if ((response.status = 1)) {
    const imagesList = response.imagesList;
  }
  const data = await response.json();
  console.log(data);
});

onBeforeUnmount(() => {
  // Clear interval when component is unmounted
  if (sliderInterval) {
    clearInterval(sliderInterval);
  }
});

/**
 * Initialize the image slider
 */
function initSlider() {
  const sliderDots = document.querySelectorAll(".slider-dot");
  const sliderItems = document.querySelectorAll(".slider-item");
  const prevButton = document.querySelector(".slider-arrow-left");
  const nextButton = document.querySelector(".slider-arrow-right");

  if (sliderDots.length === 0 || sliderItems.length === 0) return;

  // Function to change slide
  const changeSlide = (index) => {
    // Remove active class from all dots and items
    sliderDots.forEach((d) => d.classList.remove("active"));
    sliderItems.forEach((item) => {
      item.classList.remove("active");
      item.classList.remove("slide-in-right");
      item.classList.remove("slide-in-left");
    });

    // Add active class to current dot and item
    sliderDots[index].classList.add("active");

    // Add animation class based on direction
    if (
      index > currentIndex ||
      (currentIndex === sliderItems.length - 1 && index === 0)
    ) {
      sliderItems[index].classList.add("slide-in-right");
    } else {
      sliderItems[index].classList.add("slide-in-left");
    }

    sliderItems[index].classList.add("active");

    // Update current index
    currentIndex = index;

    // Reset the interval
    if (sliderInterval) {
      clearInterval(sliderInterval);
    }
    startAutoSlide();
  };

  // Set up click events for slider dots
  sliderDots.forEach((dot, index) => {
    dot.addEventListener("click", () => {
      changeSlide(index);
    });
  });

  // Set up click events for navigation arrows
  if (prevButton) {
    prevButton.addEventListener("click", () => {
      const newIndex = (currentIndex - 1 + sliderItems.length) % sliderItems.length;
      changeSlide(newIndex);
    });
  }

  if (nextButton) {
    nextButton.addEventListener("click", () => {
      const newIndex = (currentIndex + 1) % sliderItems.length;
      changeSlide(newIndex);
    });
  }

  // Function to start auto-sliding
  function startAutoSlide() {
    sliderInterval = setInterval(() => {
      const newIndex = (currentIndex + 1) % sliderDots.length;
      changeSlide(newIndex);
    }, 6000); // Slightly longer interval for better user experience
  }

  // Start auto-sliding
  startAutoSlide();
}
</script>

<style scoped>
/* Slider styles */
.slider-section {
  margin-bottom: 0;
}

.slider-container {
  position: relative;
  overflow: hidden;
  height: 500px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
  border-radius: 0;
  border-bottom: none;
}

.slider-item {
  position: absolute;
  opacity: 0;
  width: 100%;
  height: 100%;
  transition: opacity 0.8s ease-in-out;
  z-index: 1;
  overflow: hidden;
}

.slider-item.active {
  opacity: 1;
  z-index: 2;
}

.slider-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    to right,
    rgba(0, 0, 0, 0.7) 0%,
    rgba(0, 0, 0, 0.4) 50%,
    rgba(0, 0, 0, 0.1) 100%
  );
  z-index: 1;
  opacity: 0;
  transition: opacity 1s ease;
}

.slider-item.active::before {
  opacity: 1;
}

.slider-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transform: scale(1.1);
  transition: transform 8s ease-in-out;
  filter: brightness(0.9);
}

.slider-item.active img {
  transform: scale(1);
  filter: brightness(1);
}

/* Slide content animations */
.slide-content {
  opacity: 0;
  transform: translateY(20px);
}

.slider-item.active .slide-content {
  animation: fadeInUp 1s forwards 0.3s;
}

.slide-title {
  opacity: 0;
  transform: translateY(20px);
  position: relative;
  overflow: hidden;
  display: inline-block;
}

.slider-item.active .slide-title {
  animation: revealText 1.2s forwards 0.3s;
}

.slide-title::after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background: #1e40af;
  animation: textRevealBlock 1.5s 0.5s forwards cubic-bezier(0.85, 0, 0.15, 1);
  transform: translateX(-100%);
}

.slider-item.active .slide-title::after {
  animation: textRevealBlock 1.5s 0.5s forwards cubic-bezier(0.85, 0, 0.15, 1);
}

.slide-description {
  opacity: 0;
  transform: translateY(20px);
  position: relative;
  overflow: hidden;
  display: inline-block;
}

.slider-item.active .slide-description {
  animation: revealText 1.2s forwards 0.6s;
}

.slide-description::after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background: #1e40af;
  transform: translateX(-100%);
}

.slider-item.active .slide-description::after {
  animation: textRevealBlock 1.5s 0.8s forwards cubic-bezier(0.85, 0, 0.15, 1);
}

.slide-button {
  opacity: 0;
  transform: scale(0.8);
}

.slider-item.active .slide-button {
  animation: popIn 0.8s forwards 1.2s cubic-bezier(0.17, 0.67, 0.83, 0.67);
}

/* Slide direction animations */
.slide-in-right {
  animation: zoomFadeIn 1s forwards;
}

.slide-in-left {
  animation: zoomFadeIn 1s forwards;
}

/* Navigation dots */
.slider-dots-container {
  display: flex;
  background-color: rgba(0, 0, 0, 0.6);
  padding: 6px 12px;
  border-radius: 30px;
  z-index: 10;
}

.slider-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: transparent;
  border: 2px solid rgba(255, 255, 255, 0.7);
  margin: 0 8px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55);
  position: relative;
}

.slider-dot:hover {
  border-color: white;
  transform: scale(1.2);
}

.slider-dot.active {
  background-color: white;
  border-color: white;
  transform: scale(1.2);
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.slider-dot::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0);
  width: 6px;
  height: 6px;
  background-color: white;
  border-radius: 50%;
  transition: transform 0.3s ease;
}

.slider-dot.active::after {
  transform: translate(-50%, -50%) scale(1);
}

/* Navigation arrows */
.slider-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  opacity: 0.7;
  transition: all 0.3s ease;
}

.slider-arrow:hover {
  background-color: rgba(0, 0, 0, 0.8);
  opacity: 1;
  transform: translateY(-50%) scale(1.1);
}

.slider-arrow-left {
  left: 20px;
}

.slider-arrow-right {
  right: 20px;
}

/* Keyframe animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes revealText {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes textRevealBlock {
  0% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes popIn {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  70% {
    opacity: 1;
    transform: scale(1.1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes zoomFadeIn {
  0% {
    opacity: 0;
    transform: scale(1.1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulseGlow {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(255, 255, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .slider-container {
    height: 350px;
  }

  .slider-arrow {
    width: 32px;
    height: 32px;
  }

  .slider-arrow-left {
    left: 10px;
  }

  .slider-arrow-right {
    right: 10px;
  }
}

@media (max-width: 480px) {
  .slider-container {
    height: 250px;
  }

  .slide-title {
    font-size: 1.5rem;
  }

  .slide-description {
    font-size: 1rem;
  }

  .slide-button {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }
}
</style>
