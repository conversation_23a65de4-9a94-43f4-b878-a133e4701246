import axios from 'axios'
import { defineStore } from 'pinia'

export default defineStore('admissions', {
  // State
  state: () => ({
    admissions: [],
    loading: false,
    currentPage: 1,
    pageSize: 10,
    totalItems: 0,
    sortKey: '',
    sortOrder: 'asc',
    searchQuery: '',
    columnFilters: {}, // Initialize as empty object
    selectedItems: [], // Initialize as empty array
    isLoadingAllData: false
  }),

  // Getters
  getters: {
    hasSelectedItems: (state) => state.selectedItems && state.selectedItems.length > 0
  },

  // Actions
  actions: {
    async fetchAdmissions() {
      this.loading = true

      try {
        // api.post('/admissions');
        // return ;

        // Prepare parameters for axios
        const params = {
          page: this.currentPage,
          limit: this.pageSize
        }

        // Add sorting parameters if available
        if (this.sortKey) {
          params.sortBy = this.sortKey
          params.sortOrder = this.sortOrder
        }

        // Add search parameter if available
        if (this.searchQuery) {
          params.search = this.searchQuery
        }

        // Add column filters if available
        if (Object.keys(this.columnFilters).length > 0) {
          params.filters = JSON.stringify(this.columnFilters)
        }

        // Make the API call using axios
        const response = await axios.get('http://127.0.0.1:8000/api/admissions', { params })
        const result = response.data

        // Extract data from the response
        if (result.success && result.data) {
          this.admissions = result.data.data
          this.totalItems = result.data.total
          this.currentPage = result.data.current_page
        }
      } catch (error) {
        console.error('Error fetching admissions:', error)
      } finally {
        this.loading = false
      }
    },

    // Handle page change
    handlePageChange(page) {
      this.currentPage = page
      this.fetchAdmissions()
    },

    // Handle page size change
    handlePageSizeChange(size) {
      this.pageSize = size
      this.currentPage = 1 // Reset to first page
      this.fetchAdmissions()
    },

    // Handle sort
    handleSort({ key, order }) {
      this.sortKey = key
      this.sortOrder = order
      this.fetchAdmissions()
    },

    // Handle search
    handleSearch(query) {
      this.searchQuery = query
      this.currentPage = 1 // Reset to first page
      this.fetchAdmissions()
    },

    // Handle column filter
    handleColumnFilter(filters) {
      this.columnFilters = filters
      this.currentPage = 1 // Reset to first page
      this.fetchAdmissions()
    },

    // Handle selection change
    handleSelectionChange(items) {
      this.selectedItems = items
    },

    // Handle fetch all data for export/print
    async fetchAllData({ search, filters, sort, callback }) {
      this.isLoadingAllData = true

      try {
        // Prepare parameters for axios
        const params = {
          // Request all records by setting no_pagination=true
          no_pagination: true,
          page: 1
        }

        // Add search parameter if available
        if (search) {
          params.search = search
        }

        // Add sort parameters if available
        if (sort && sort.key) {
          params.sortBy = sort.key
          params.sortOrder = sort.order
        }

        // Add filters if available
        if (filters && Object.keys(filters).length > 0) {
          params.filters = JSON.stringify(filters)
        }

        // Make the API call using axios
        const response = await axios.get('http://127.0.0.1:8000/api/admissions', { params })
        const result = response.data

        // Extract data from the response - handle different response formats
        let responseData = []

        // Handle different API response formats
        if (result.success && result.data) {
          // Format 1: Nested data.data array (paginated response)
          if (Array.isArray(result.data.data)) {
            responseData = result.data.data
          }
          // Format 2: Direct data array (when no_pagination is used)
          else if (Array.isArray(result.data)) {
            responseData = result.data
          }
          // Format 3: Single data object
          else if (typeof result.data === 'object') {
            responseData = [result.data]
          }
        }

        console.log(`Fetched ${responseData.length} records for export/print`)

        // Return the data through the callback
        callback(responseData)
        return responseData
      } catch (error) {
        console.error('Error fetching all data:', error)
        callback([])
        return []
      } finally {
        this.isLoadingAllData = false
      }
    },

    // Action handlers
    viewAdmission(admission) {
      alert(`View admission for: ${admission.child_name}`)
    },

    editAdmission(admission) {
      alert(`Edit admission for: ${admission.child_name}`)
    },

    deleteAdmission(admission) {
      if (confirm(`Are you sure you want to delete admission for ${admission.child_name}?`)) {
        alert(`Admission for ${admission.child_name} would be deleted in a real application`)
      }
    },

    // Helper function to format date
    formatDate(dateString) {
      if (!dateString) return ''

      const date = new Date(dateString)
      return date.toLocaleDateString('en-US', { 
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      })
    }
  }
})
