import { defineStore } from 'pinia'

import api from '@/api/api'
import { ORG_ID } from '@/api/endpoint'

export const useOrganizationStore = defineStore('organization', {
    state: () => ({
        organizationDetail: null,
        loading: false,
        error: null,
    }),
    actions: {
        async fetchOrganizationDetail() {
            this.loading = true;
            this.error = null;
            try {
                const response = await api.get(`/customer/organizationDetail/${ORG_ID}`);
                if (response.data.status === 1) {
                    this.organizationDetail = response.data;
                } else {
                    this.error = 'Failed to fetch organization details';
                }
            } catch (error) {
                this.error = error;
            } finally {
                this.loading = false;
            }
        },
    },
});
