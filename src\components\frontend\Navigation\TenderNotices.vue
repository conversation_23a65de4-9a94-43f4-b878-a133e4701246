<template>
  <section class="annual-reports-section py-6 bg-gradient-to-br from-blue-50 to-blue-100">
    <div class="container mx-auto px-4">
      <div class="max-w-5xl mx-auto">
        <!-- Header -->
        <div class="text-center mb-4">
          <h2
            class="text-xl md:text-2xl font-bold text-blue-600 mb-1 relative inline-block"
          >
            TenderNotice
            <span
              class="absolute bottom-0 left-0 w-full h-0.5 bg-gradient-to-r from-blue-400 to-blue-200 -mb-1"
            ></span>
          </h2>
          <p class="text-sm text-gray-600 mt-1">Access and download our TenderNotice</p>
        </div>

        <!-- Back to Categories Button -->
        <div class="mb-3 inline-block">
          <router-link
            to="/categories"
            class="flex items-center bg-blue-900 text-white px-3 py-1 text-sm rounded-md hover:bg-blue-800 transition-colors"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 mr-1"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                clip-rule="evenodd"
              />
            </svg>
            Categories
          </router-link>
        </div>

        <!-- Reports List -->
        <div class="bg-white rounded-xl shadow-lg overflow-hidden">
          <div class="p-3">
            <h3 class="text-lg font-bold text-blue-900 mb-2 border-b pb-1">
              Download Report
            </h3>

            <div class="space-y-2">
              <!-- Report Items - Dynamically rendered from reports array -->
              <div
                v-for="report in paginatedReports"
                :key="report.id"
                class="report-item p-2 border-b border-gray-100 flex flex-wrap justify-between items-center hover:bg-blue-50 transition-colors rounded-md"
              >
                <div class="flex items-start space-x-2 mb-1 md:mb-0">
                  <div class="flex-shrink-0">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-5 w-5 text-red-500"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"
                      />
                    </svg>
                  </div>
                  <div>
                    <h4 class="font-medium text-gray-800 text-sm">{{ report.title }}</h4>
                    <p class="text-xs text-gray-500">{{ report.filename }}</p>
                  </div>
                </div>
                <div class="flex space-x-1">
                  <button
                    @click="viewDetails(report)"
                    class="bg-cyan-500 hover:bg-cyan-600 text-white px-3 py-1 rounded-md text-xs transition-colors relative group"
                  >
                    Details
                    <!-- Tooltip on hover -->
                    <div
                      class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 w-48 bg-gray-800 text-white text-xs rounded py-2 px-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none z-10"
                    >
                      <div class="text-left">
                        <p><strong>Title:</strong> {{ report.title }}</p>
                        <p><strong>Filename:</strong> {{ report.filename }}</p>
                        <p><strong>Size:</strong> {{ report.fileSize || "1.2 MB" }}</p>
                        <p>
                          <strong>Published:</strong>
                          {{ report.publishDate || "2023-01-15" }}
                        </p>
                      </div>
                      <div
                        class="absolute top-full left-1/2 transform -translate-x-1/2 border-8 border-transparent border-t-gray-800"
                      ></div>
                    </div>
                  </button>
                  <button
                    @click="downloadReport(report)"
                    class="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded-md text-xs transition-colors"
                  >
                    Download
                  </button>
                </div>
              </div>

              <!-- No reports message -->
              <div
                v-if="paginatedReports.length === 0"
                class="text-center py-4 text-gray-500"
              >
                No reports available
              </div>
            </div>
          </div>
        </div>

        <!-- Pagination Controls -->
        <div class="mt-3 flex justify-between items-center">
          <div class="text-sm text-gray-600">
            Showing {{ paginatedReports.length }} of {{ reports.length }} reports
          </div>
          <div class="flex items-center">
            <span class="mr-2 text-sm text-gray-600">Display Num</span>
            <select
              v-model="displayCount"
              @change="currentPage = 1"
              class="border rounded-md px-2 py-1 text-sm bg-white"
            >
              <option v-for="option in displayOptions" :key="option" :value="option">
                {{ option }}
              </option>
            </select>
          </div>
        </div>

        <!-- Page Navigation -->
        <div v-if="totalPages > 1" class="mt-4 flex justify-center">
          <div class="flex space-x-1">
            <button
              @click="prevPage"
              :disabled="currentPage === 1"
              :class="[
                'px-3 py-1 rounded-md text-sm',
                currentPage === 1
                  ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
                  : 'bg-blue-500 text-white hover:bg-blue-600',
              ]"
            >
              Previous
            </button>
            <button
              v-for="page in displayedPageNumbers"
              :key="page"
              @click="goToPage(page)"
              :class="[
                'px-3 py-1 rounded-md text-sm',
                currentPage === page
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200',
              ]"
            >
              {{ page }}
            </button>
            <button
              @click="nextPage"
              :disabled="currentPage === totalPages"
              :class="[
                'px-3 py-1 rounded-md text-sm',
                currentPage === totalPages
                  ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
                  : 'bg-blue-500 text-white hover:bg-blue-600',
              ]"
            >
              Next
            </button>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import {
  computed,
  ref,
} from 'vue'

import axios from 'axios'

// State for reports data
const reports = ref([
  {
    id: 1,
    title: "Download Report 80/81",
    filename: "Download Report 80.81.pdf",
    fileUrl: "/reports/annual-report-80-81.pdf",
    fileSize: "1.8 MB",
    publishDate: "2024-01-15",
  },
  {
    id: 2,
    title: "Download Report 2073-2074",
    filename: "2073-74.pdf",
    fileUrl: "/reports/2073-74.pdf",
    fileSize: "2.3 MB",
    publishDate: "2023-12-10",
  },
  {
    id: 3,
    title: "Download Report 2074-2075",
    filename: "BWSMB-Download Report 2074-075.pdf",
    fileUrl: "/reports/BWSMB-Annual-Report-2074-075.pdf",
    fileSize: "3.1 MB",
    publishDate: "2022-11-05",
  },
  {
    id: 4,
    title: "Download Report 2075-2076",
    filename: "annual final 2075-076.pdf",
    fileUrl: "/reports/annual-final-2075-076.pdf",
    fileSize: "2.7 MB",
    publishDate: "2021-10-20",
  },
  {
    id: 5,
    title: "Download Report 2072-2073",
    filename: "barsik pratibedan 2072-73.pdf",
    fileUrl: "/reports/barsik-pratibedan-2072-73.pdf",
    fileSize: "1.9 MB",
    publishDate: "2020-09-15",
  },
  {
    id: 6,
    title: "Download Report 2071-2072",
    filename: "Download Report 2071-72.pdf",
    fileUrl: "/reports/Annual-Report-2071-72.pdf",
    fileSize: "2.5 MB",
    publishDate: "2019-08-10",
  },
  {
    id: 7,
    title: "Download Report 2070-2071",
    filename: "Download Report 2070-2071.pdf",
    fileUrl: "/reports/Annual-Report-2070-2071.pdf",
    fileSize: "2.2 MB",
    publishDate: "2018-07-05",
  },
  {
    id: 8,
    title: "Download Report 2069-2070",
    filename: "Download Report 2069-70.pdf",
    fileUrl: "/reports/Annual-Report-2069-70.pdf",
    fileSize: "1.7 MB",
    publishDate: "2017-06-01",
  },
  {
    id: 9,
    title: "Download Report 2068-2069",
    filename: "Download Report 2068-69.pdf",
    fileUrl: "/reports/Annual-Report-2068-69.pdf",
    fileSize: "2.1 MB",
    publishDate: "2016-05-15",
  },
  {
    id: 10,
    title: "Download Report 2067-2068",
    filename: "Download Report 2067-68.pdf",
    fileUrl: "/reports/Annual-Report-2067-68.pdf",
    fileSize: "1.9 MB",
    publishDate: "2015-04-20",
  },
  {
    id: 11,
    title: "Download Report 2066-2067",
    filename: "Download Report 2066-67.pdf",
    fileUrl: "/reports/Annual-Report-2066-67.pdf",
    fileSize: "2.3 MB",
    publishDate: "2014-03-25",
  },
  {
    id: 12,
    title: "Download Report 2065-2066",
    filename: "Download Report 2065-66.pdf",
    fileUrl: "/reports/Annual-Report-2065-66.pdf",
    fileSize: "2.0 MB",
    publishDate: "2013-02-10",
  },
]);

// Pagination state
const displayCount = ref(10);
const displayOptions = [10, 20, 50, 100];
const currentPage = ref(1);

// Computed properties for pagination
const totalPages = computed(() => Math.ceil(reports.value.length / displayCount.value));

const paginatedReports = computed(() => {
  const start = (currentPage.value - 1) * displayCount.value;
  const end = start + displayCount.value;
  return reports.value.slice(start, end);
});

// Display only a reasonable number of page buttons
const displayedPageNumbers = computed(() => {
  const pages = [];
  const maxVisiblePages = 5;

  if (totalPages.value <= maxVisiblePages) {
    // If we have fewer pages than our max, show all pages
    for (let i = 1; i <= totalPages.value; i++) {
      pages.push(i);
    }
  } else {
    // Always include first page
    pages.push(1);

    // Calculate start and end of visible page range
    let startPage = Math.max(2, currentPage.value - 1);
    let endPage = Math.min(totalPages.value - 1, currentPage.value + 1);

    // Adjust if we're near the beginning
    if (currentPage.value <= 3) {
      endPage = Math.min(totalPages.value - 1, 4);
    }

    // Adjust if we're near the end
    if (currentPage.value >= totalPages.value - 2) {
      startPage = Math.max(2, totalPages.value - 3);
    }

    // Add ellipsis if needed
    if (startPage > 2) {
      pages.push("...");
    }

    // Add visible page numbers
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    // Add ellipsis if needed
    if (endPage < totalPages.value - 1) {
      pages.push("...");
    }

    // Always include last page
    if (totalPages.value > 1) {
      pages.push(totalPages.value);
    }
  }

  return pages;
});

// Pagination methods
const goToPage = (page) => {
  if (typeof page === "number") {
    currentPage.value = page;
  }
};

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
  }
};

const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--;
  }
};

// Function to fetch reports from API (to be implemented later)
const fetchReports = async () => {
  try {
    // Uncomment when API is ready
    // const response = await axios.get('/api/reports')
    // reports.value = response.data
  } catch (error) {
    console.error("Error fetching reports:", error);
  }
};

// Function to view report details
const viewDetails = (report) => {
  console.log("View details for:", report.title);
  // Implement details view logic
};

// Function to download report
const downloadReport = (report) => {
  console.log("Downloading:", report.title);
  // Implement download logic
  window.open(report.fileUrl, "_blank");
};

// Uncomment when ready to fetch from API
// onMounted(() => {
//   fetchReports()
// })
</script>

<style scoped>
.report-item:last-child {
  border-bottom: none;
}
</style>
