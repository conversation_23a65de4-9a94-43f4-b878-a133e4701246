<template>
  <ul class="sidebar_nav">
    <template v-for="(item, index) in menuItems" :key="index">
      <!-- Menu Title -->
      <li v-if="item.type === 'title'" class="menu-title">
        <span>{{ item.label }}</span>
      </li>

      <!-- Regular Menu Item -->
      <li v-else :class="[
        item.children ? 'has-child' : '',
        isMenuActive(item) ? 'active' : '',
        isMenuOpen(item) ? 'open' : ''
      ]">
        <a href="#" @click.prevent="handleMenuClick(item)" :class="{ 'active': isMenuActive(item) }">
          <span v-if="item.icon" class="nav-icon" :class="item.icon"></span>
          <span class="menu-text">{{ item.label }}</span>
          <span v-if="item.children" class="toggle-icon"></span>
          <span v-if="item.badge" class="menuItem" :class="item.badge.variant">{{ item.badge.text }}</span>
        </a>

        <!-- Submenu (Recursive) -->
        <template v-if="item.children">
          <ul v-show="isMenuOpen(item)">
            <li v-for="(child, childIndex) in item.children" :key="childIndex"
              :class="{ 'active': isMenuActive(child) }">
              <a href="#" @click.prevent="handleMenuClick(child)" :class="{ 'active': isMenuActive(child) }">
                <span v-if="child.icon" class="nav-icon" :class="child.icon"></span>
                <span>{{ child.label }}</span>
                <span v-if="child.badge" class="menuItem" :class="child.badge.variant">{{ child.badge.text }}</span>
              </a>

              <!-- Third Level Menu (if needed) -->
              <template v-if="child.children">
                <ul v-show="isMenuOpen(child)">
                  <li v-for="(grandChild, grandChildIndex) in child.children" :key="grandChildIndex"
                    :class="{ 'active': isMenuActive(grandChild) }">
                    <a href="#" @click.prevent="handleMenuClick(grandChild)"
                      :class="{ 'active': isMenuActive(grandChild) }">
                      <span v-if="grandChild.icon" class="nav-icon" :class="grandChild.icon"></span>
                      <span>{{ grandChild.label }}</span>
                      <span v-if="grandChild.badge" class="menuItem" :class="grandChild.badge.variant">{{
                        grandChild.badge.text }}</span>
                    </a>
                  </li>
                </ul>
              </template>
            </li>
          </ul>
        </template>
      </li>
    </template>
  </ul>
</template>

<script setup>
import {
  onMounted,
  ref,
} from 'vue'

import {
  useRoute,
  useRouter,
} from 'vue-router'

// Props
const props = defineProps({
  menuItems: {
    type: Array,
    required: true
  }
})

const router = useRouter()
const route = useRoute()

// Track open menu items
const openMenus = ref([])

// Initialize open menus based on current route
onMounted(() => {
  // Find active menu items and open their parents
  const findActiveItems = (items, parentIds = []) => {
    items.forEach(item => {
      if (isMenuActive(item)) {
        // Add all parent IDs to openMenus
        parentIds.forEach(id => {
          if (!openMenus.value.includes(id)) {
            openMenus.value.push(id)
          }
        })
      }

      if (item.children) {
        findActiveItems(item.children, [...parentIds, item.id])
      }
    })
  }

  findActiveItems(props.menuItems)
})

// Check if a menu item is active based on current route
const isMenuActive = (item) => {
  if (!item.route) return false

  // Exact match
  if (item.route === route.path) return true

  // Check if current route starts with the menu item's route (for parent routes)
  if (item.exact === false && route.path.startsWith(item.route)) return true

  return false
}

// Check if a menu item is open
const isMenuOpen = (item) => {
  // If the item or any of its children is active, it should be open
  if (isMenuActive(item)) return true

  // Check if any child is active
  if (item.children) {
    const hasActiveChild = item.children.some(child => {
      if (isMenuActive(child)) return true
      if (child.children) {
        return child.children.some(grandChild => isMenuActive(grandChild))
      }
      return false
    })

    if (hasActiveChild) return true
  }

  // Check if it's in the openMenus array
  return openMenus.value.includes(item.id)
}

// Handle menu item click
const handleMenuClick = (item) => {
  // If the item has children, toggle its open state
  if (item.children) {
    toggleMenu(item)
  }
  // If the item has a route, navigate to it
  else if (item.route) {
    router.push(item.route)
  }
}

// Toggle menu open/close state
const toggleMenu = (item) => {
  const index = openMenus.value.indexOf(item.id)
  if (index === -1) {
    openMenus.value.push(item.id)
  } else {
    openMenus.value.splice(index, 1)
  }
}
</script>
