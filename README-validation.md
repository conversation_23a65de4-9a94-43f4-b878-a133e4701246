# Vue Form Validation System

A comprehensive, easy-to-use validation system for Vue 3 applications with great design and simple implementation.

## File Structure

```
src/
├── assets/
│   └── validation.css           # Validation styles
├── components/
│   └── FormValidation/
│       ├── index.js             # Export file for components
│       ├── VForm.vue            # Form wrapper component
│       ├── VInput.vue           # Input component
│       ├── VSelect.vue          # Select component
│       ├── VTextarea.vue        # Textarea component
│       ├── VCheckbox.vue        # Checkbox component
│       ├── VRadio.vue           # Radio button component
│       └── ExampleForm.vue      # Example implementation
└── utils/
    └── validation.js            # Core validation utility
```

## When to Use Each File

### Core Files

- **src/utils/validation.js**: Import this file when you need to define validation rules for your forms. It contains all the built-in validation rules and validation logic.

  ```javascript
  import { rules } from '@/utils/validation'
  ```

- **src/components/FormValidation/index.js**: This file exports all the validation components. You don't need to import this directly as the components are registered globally in main.js.

### Component Files

- **VForm.vue**: Always use this as the parent container for your form. It manages form state and validation.
- **VInput.vue**: Use for text, email, password, number, and other input types.
- **VSelect.vue**: Use for dropdown selection fields.
- **VTextarea.vue**: Use for multi-line text input.
- **VCheckbox.vue**: Use for boolean values or multiple selections.
- **VRadio.vue**: Use for single selection from multiple options.
- **ExampleForm.vue**: Reference this file for a complete example of how to use the validation system.

### Style File

- **src/assets/validation.css**: This file contains the styles for validation states. It's automatically imported in main.js, so you don't need to import it manually.

## Features

- Simple, declarative validation rules
- Built-in common validation rules (required, email, min/max length, etc.)
- Support for custom validation rules
- Async validation support
- Form-level and field-level validation
- Automatic error display
- Customizable styling
- Fully typed components (TypeScript friendly)
- Seamless integration with existing UI

## Components

The validation system includes the following components:

- `VForm` - Form wrapper component that manages validation state
- `VInput` - Validated input component
- `VSelect` - Validated select component
- `VTextarea` - Validated textarea component
- `VCheckbox` - Validated checkbox component
- `VRadio` - Validated radio button component

## Basic Usage

### Simple Form Example

```vue
<template>
  <VForm
    :initial-values="formData"
    :validation-rules="validationRules"
    @submit="handleSubmit"
  >
    <template #default="{ values, errors, isSubmitting }">
      <VInput
        v-model="values.name"
        name="name"
        label="Name"
        placeholder="Enter your name"
        :validations="validationRules.name"
        :form-values="values"
      />

      <VInput
        v-model="values.email"
        name="email"
        label="Email"
        type="email"
        placeholder="Enter your email"
        :validations="validationRules.email"
        :form-values="values"
      />

      <VTextarea
        v-model="values.message"
        name="message"
        label="Message"
        placeholder="Enter your message"
        :validations="validationRules.message"
        :form-values="values"
      />

      <button type="submit" class="btn btn-primary" :disabled="isSubmitting">
        {{ isSubmitting ? 'Submitting...' : 'Submit' }}
      </button>
    </template>
  </VForm>
</template>

<script setup>
import { ref } from 'vue'
import { rules } from '@/utils/validation'

const formData = ref({
  name: '',
  email: '',
  message: ''
})

const validationRules = {
  name: [rules.required, rules.minLength(3)],
  email: [rules.required, rules.email],
  message: [rules.required, rules.minLength(10)]
}

const handleSubmit = ({ values, valid }) => {
  if (valid) {
    // Process form submission
    console.log('Form submitted:', values)
  }
}
</script>
```

## Validation Rules

### Built-in Rules

The validation system comes with several built-in rules:

- `required` - Ensures the field is not empty
- `email` - Validates email format
- `minLength(min)` - Ensures the field has at least `min` characters
- `maxLength(max)` - Ensures the field has at most `max` characters
- `numeric` - Ensures the field contains only numbers
- `integer` - Ensures the field contains only integers
- `min(min)` - Ensures the numeric value is at least `min`
- `max(max)` - Ensures the numeric value is at most `max`
- `pattern(regex, message)` - Validates against a custom regex pattern
- `match(field, fieldName)` - Ensures the field matches another field (useful for password confirmation)

### Custom Rules

You can create custom validation rules easily:

```javascript
// Custom validation rule
const isStrongPassword = (value) => {
  if (!value) return true // Skip if empty

  const hasUpperCase = /[A-Z]/.test(value)
  const hasLowerCase = /[a-z]/.test(value)
  const hasNumbers = /\d/.test(value)
  const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(value)

  return (
    hasUpperCase &&
    hasLowerCase &&
    hasNumbers &&
    hasSpecialChar
  ) || 'Password must include uppercase, lowercase, number, and special character'
}

// Usage
const validationRules = {
  password: [rules.required, rules.minLength(8), isStrongPassword]
}
```

### Async Validation

The validation system supports async validation for server-side checks:

```javascript
// Async validation rule
const isUsernameAvailable = async (value) => {
  if (!value) return true // Skip if empty

  try {
    const response = await api.get(`/check-username?username=${value}`)
    return response.data.available || 'Username is already taken'
  } catch (error) {
    return 'Error checking username availability'
  }
}

// Usage
const validationRules = {
  username: [rules.required, rules.minLength(3), isUsernameAvailable]
}
```

## Component Props

### VForm

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| initialValues | Object | {} | Initial form values |
| validationRules | Object | {} | Validation rules for each field |
| validateOnMount | Boolean | false | Whether to validate on component mount |

### VInput / VTextarea

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| modelValue | String/Number | '' | v-model value |
| label | String | '' | Input label |
| name | String | '' | Input name attribute |
| type | String | 'text' | Input type (for VInput) |
| placeholder | String | '' | Input placeholder |
| validations | Array | [] | Array of validation rules |
| formValues | Object | {} | All form values (for cross-field validation) |
| validateOnBlur | Boolean | true | Whether to validate on blur |
| validateOnChange | Boolean | false | Whether to validate on change |
| validateOnMount | Boolean | false | Whether to validate on mount |
| customClass | String | '' | Additional CSS classes |
| size | String | 'medium' | Input size ('small', 'medium', 'large') |
| disabled | Boolean | false | Whether the input is disabled |
| readonly | Boolean | false | Whether the input is readonly |
| icon | String | '' | Icon class (for VInput) |
| iconPosition | String | 'left' | Icon position ('left', 'right') |
| rows | Number/String | 4 | Number of rows (for VTextarea) |

### VSelect

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| modelValue | String/Number/Array | '' | v-model value |
| label | String | '' | Select label |
| name | String | '' | Select name attribute |
| options | Array | [] | Select options |
| placeholder | String | 'Select an option' | Select placeholder |
| validations | Array | [] | Array of validation rules |
| formValues | Object | {} | All form values (for cross-field validation) |
| validateOnBlur | Boolean | true | Whether to validate on blur |
| validateOnChange | Boolean | true | Whether to validate on change |
| validateOnMount | Boolean | false | Whether to validate on mount |
| customClass | String | '' | Additional CSS classes |
| size | String | 'medium' | Select size ('small', 'medium', 'large') |
| disabled | Boolean | false | Whether the select is disabled |
| multiple | Boolean | false | Whether multiple selection is allowed |
| valueKey | String | 'value' | Key for option value when options are objects |
| labelKey | String | 'label' | Key for option label when options are objects |

### VCheckbox / VRadio

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| modelValue | Boolean/Array/String/Number | false | v-model value |
| label | String | '' | Checkbox/Radio label |
| name | String | '' | Checkbox/Radio name attribute |
| value | String/Number/Boolean | true | Value when checked (important for radio buttons and checkbox arrays) |
| validations | Array | [] | Array of validation rules |
| formValues | Object | {} | All form values (for cross-field validation) |
| validateOnChange | Boolean | true | Whether to validate on change |
| validateOnMount | Boolean | false | Whether to validate on mount |
| customClass | String | '' | Additional CSS classes |
| disabled | Boolean | false | Whether the checkbox/radio is disabled |

## Events

### VForm

| Event | Payload | Description |
|-------|---------|-------------|
| submit | { values, valid, errors } | Emitted when the form is submitted |
| validation | { valid, errors } | Emitted when the form is validated |
| reset | - | Emitted when the form is reset |

### VInput / VSelect / VTextarea / VCheckbox / VRadio

| Event | Payload | Description |
|-------|---------|-------------|
| update:modelValue | value | Emitted when the value changes |
| validation | { valid, errors, field } | Emitted when the field is validated |
| blur | event | Emitted when the field loses focus |
| focus | event | Emitted when the field gains focus |
| input/change | event | Emitted when the field value changes |

## Styling

The validation system comes with default styling that integrates with Bootstrap-like CSS classes. You can customize the styling by modifying the `validation.css` file or by providing custom classes to the components.

## Advanced Usage

### Form with Custom Validation Logic

```vue
<template>
  <VForm
    ref="form"
    :initial-values="formData"
    :validation-rules="validationRules"
    @submit="handleSubmit"
  >
    <template #default="{ values, errors, isSubmitting, validate }">
      <!-- Form fields -->

      <button type="button" @click="validateCustomField">
        Validate Custom Field
      </button>

      <button type="submit" class="btn btn-primary" :disabled="isSubmitting">
        Submit
      </button>
    </template>
  </VForm>
</template>

<script setup>
import { ref } from 'vue'
import { rules } from '@/utils/validation'

const form = ref(null)

const validateCustomField = async () => {
  // Custom validation logic
  const isValid = await form.value.validate()

  if (isValid) {
    // Do something
  }
}
</script>
```

### Conditional Validation

```vue
<script setup>
import { ref, computed } from 'vue'
import { rules } from '@/utils/validation'

const formData = ref({
  contactMethod: 'email',
  email: '',
  phone: ''
})

const validationRules = computed(() => {
  const baseRules = {
    contactMethod: [rules.required]
  }

  if (formData.value.contactMethod === 'email') {
    baseRules.email = [rules.required, rules.email]
  } else if (formData.value.contactMethod === 'phone') {
    baseRules.phone = [rules.required, rules.pattern(/^\d{10}$/, 'Phone must be 10 digits')]
  }

  return baseRules
})
</script>
```

## Best Practices

1. **Keep validation rules separate** - Define validation rules outside of components for reusability
2. **Use computed properties for dynamic validation** - When validation rules depend on form values
3. **Provide clear error messages** - Make error messages helpful and specific
4. **Validate at appropriate times** - Consider when to trigger validation (blur, change, submit)
5. **Group related fields** - Use fieldsets or sections for related form fields
6. **Test validation thoroughly** - Ensure validation works as expected in all scenarios

## Quick Start Guide

### Step 1: Import Validation Rules

First, import the validation rules in your component:

```javascript
import { rules } from '@/utils/validation'
```

### Step 2: Define Form Data and Validation Rules

```javascript
const formData = ref({
  name: '',
  email: '',
  password: ''
})

const validationRules = {
  name: [rules.required, rules.minLength(3)],
  email: [rules.required, rules.email],
  password: [rules.required, rules.minLength(8)]
}
```

### Step 3: Create the Form

```vue
<VForm
  :initial-values="formData"
  :validation-rules="validationRules"
  @submit="handleSubmit"
>
  <template #default="{ values }">
    <VInput
      v-model="values.name"
      name="name"
      label="Name"
      :validations="validationRules.name"
      :form-values="values"
    />

    <!-- Add more form fields -->

    <button type="submit" class="btn btn-primary">Submit</button>
  </template>
</VForm>
```

### Step 4: Handle Form Submission

```javascript
const handleSubmit = ({ values, valid }) => {
  if (valid) {
    // Process form submission
    console.log('Form submitted:', values)
  }
}
```

## Example Implementation

For a complete working example, check out `src/components/FormValidation/ExampleForm.vue`. This file demonstrates all the validation components and features in action.

## Troubleshooting

### Common Issues

- **Validation not triggering**: Ensure you're passing the correct validation rules and form values
- **Cross-field validation not working**: Make sure you're passing the entire form values object
- **Custom validation rules not working**: Check that your custom rule returns either true or an error message
- **Styling issues**: Check that the validation.css file is properly imported

### Error Messages Not Showing

If error messages are not showing:

1. Make sure you're passing the validation rules to the component
2. Check that the validation rules are correctly defined
3. Ensure the form values are being updated correctly

### Component Not Found

If you get a "component not found" error:

1. Make sure the components are registered globally in `main.js`
2. Check that you're using the correct component name
3. Try restarting your development server

---

## Need Help?

If you have any questions or need help with the validation system, please refer to the example implementation in `src/components/FormValidation/ExampleForm.vue`.

---

Happy coding! 🚀
