<template>
  <nav class="bg-[#001755] text-white sticky-nav">
    <div class="container mx-auto px-4">
      <!-- Mobile menu button -->
      <div class="md:hidden py-2 flex justify-end">
        <button id="mobile-menu-button" class="text-white focus:outline-none">
          <i class="fas fa-bars text-xl"></i>
        </button>
      </div>

      <!-- Desktop menu -->
      <div class="hidden md:flex md:flex-wrap items-center justify-center">
        <router-link to="/" class="nav-item flex items-center"
          ><i class="fas fa-home mr-1"></i> Home</router-link
        >

        <!-- Organization Dropdown -->
        <div class="relative group">
          <button class="nav-item flex items-center">
            <i class="fas fa-sitemap mr-1"></i> Organization
            <i class="fas fa-chevron-down ml-1 text-xs"></i>
          </button>
          <div
            class="absolute left-0 mt-0 w-48 bg-white shadow-lg rounded-b hidden group-hover:block z-10"
          >
            <router-link
              to="/provision-executive-director"
              class="block px-4 py-2 text-gray-800 hover:bg-blue-100"
              >Provisions Related to Executive Director</router-link
            >
            <router-link
              to="/members"
              class="block px-4 py-2 text-gray-800 hover:bg-blue-100"
              >Member</router-link
            >
          </div>
        </div>

        <router-link to="/miscellaneous" class="nav-item flex items-center"
          ><i class="fas fa-random mr-1"></i> Miscellaneous</router-link
        >

        <!-- Publications Dropdown -->
        <div class="relative group">
          <button class="nav-item flex items-center">
            <i class="fas fa-file-alt mr-1"></i> Publications
            <i class="fas fa-chevron-down ml-1 text-xs"></i>
          </button>
          <div
            class="absolute left-0 mt-0 w-48 bg-white shadow-lg rounded-b hidden group-hover:block z-10"
          >
            <router-link
              to="/publications/annual-reports"
              class="block px-4 py-2 text-gray-800 hover:bg-blue-100"
              >Annual Reports</router-link
            >
            <router-link
              to="/publications/audit-reports"
              class="block px-4 py-2 text-gray-800 hover:bg-blue-100"
              >Audit Reports</router-link
            >
            <router-link
              to="/publications/water-quality-test-report"
              class="block px-4 py-2 text-gray-800 hover:bg-blue-100"
              >Water Quality Test Report</router-link
            >
            <router-link
              to="/publications/downloads"
              class="block px-4 py-2 text-gray-800 hover:bg-blue-100"
              >Downloads</router-link
            >
          </div>
        </div>

        <!-- Notices Dropdown -->
        <div class="relative group">
          <button class="nav-item flex items-center">
            <i class="fas fa-bell mr-1"></i> Notices
            <i class="fas fa-chevron-down ml-1 text-xs"></i>
          </button>
          <div
            class="absolute left-0 mt-0 w-48 bg-white shadow-lg rounded-b hidden group-hover:block z-10"
          >
            <router-link
              to="/notices/tender-notices"
              class="block px-4 py-2 text-gray-800 hover:bg-blue-100"
              >Tender Notices</router-link
            >
            <!-- <router-link
              to="/notices/vacancy"
              class="block px-4 py-2 text-gray-800 hover:bg-blue-100"
              >Vacancy</router-link
            > -->
            <router-link
              to="/notices/tariff-and-others"
              class="block px-4 py-2 text-gray-800 hover:bg-blue-100"
              >Tarrif and Others</router-link
            >
            <router-link
              to="/notices/consumer-notice"
              class="block px-4 py-2 text-gray-800 hover:bg-blue-100"
              >Consumer Notice</router-link
            >
          </div>
        </div>

        <router-link to="/career" class="nav-item flex items-center"
          ><i class="fas fa-briefcase mr-1"></i> Career</router-link
        >
        <router-link to="/gallery" class="nav-item flex items-center"
          ><i class="fas fa-images mr-1"></i> Gallery</router-link
        >
        <router-link to="/contact" class="nav-item flex items-center"
          ><i class="fas fa-envelope mr-1"></i> Contact</router-link
        >
      </div>

      <!-- Mobile menu -->
      <div id="mobile-menu" class="md:hidden hidden flex flex-col">
        <router-link to="/" class="nav-item border-b border-blue-800"
          ><i class="fas fa-home mr-1"></i> Home</router-link
        >

        <!-- Mobile Organization Dropdown -->
        <div class="mobile-dropdown">
          <button
            class="nav-item border-b border-blue-800 w-full text-left flex justify-between items-center"
          >
            <span><i class="fas fa-sitemap mr-1"></i> Organization</span>
            <i class="fas fa-chevron-down"></i>
          </button>
          <div class="hidden pl-4 bg-[#001f80]">
            <router-link
              to="/provision-executive-director"
              class="nav-item border-b border-blue-800 block"
              >Provisions Related to Executive Director</router-link
            >
            <router-link to="/members" class="nav-item border-b border-blue-800 block"
              >Member</router-link
            >
          </div>
        </div>

        <router-link to="/miscellaneous" class="nav-item border-b border-blue-800"
          ><i class="fas fa-random mr-1"></i> Miscellaneous</router-link
        >

        <!-- Mobile Publications Dropdown -->
        <div class="mobile-dropdown">
          <button
            class="nav-item border-b border-blue-800 w-full text-left flex justify-between items-center"
          >
            <span><i class="fas fa-file-alt mr-1"></i> Publications</span>
            <i class="fas fa-chevron-down"></i>
          </button>
          <div class="hidden pl-4 bg-[#001f80]">
            <router-link
              to="/publications/annual-reports"
              class="nav-item border-b border-blue-800 block"
              >Annual Reports</router-link
            >
            <router-link
              to="/publications/audit-reports"
              class="nav-item border-b border-blue-800 block"
              >Audit Reports</router-link
            >
            <router-link
              to="/publications/water-quality-test-report"
              class="nav-item border-b border-blue-800 block"
              >Water Quality Test Report</router-link
            >
            <router-link
              to="/publications/downloads"
              class="nav-item border-b border-blue-800 block"
              >Downloads</router-link
            >
          </div>
        </div>

        <!-- Mobile Notices Dropdown -->
        <div class="mobile-dropdown">
          <button
            class="nav-item border-b border-blue-800 w-full text-left flex justify-between items-center"
          >
            <span><i class="fas fa-bell mr-1"></i> Notices</span>
            <i class="fas fa-chevron-down"></i>
          </button>
          <div class="hidden pl-4 bg-[#001f80]">
            <router-link
              to="/notices/tender-notices"
              class="nav-item border-b border-blue-800 block"
              >Tender Notices</router-link
            >
            <router-link
              to="/notices/vacancy"
              class="nav-item border-b border-blue-800 block"
              >Vacancy</router-link
            >
            <router-link
              to="/notices/tariff-and-others"
              class="nav-item border-b border-blue-800 block"
              >Tarrif and Others</router-link
            >
            <router-link
              to="/notices/consumer-notice"
              class="nav-item border-b border-blue-800 block"
              >Consumer Notice</router-link
            >
          </div>
        </div>

        <router-link to="/career" class="nav-item border-b border-blue-800"
          ><i class="fas fa-briefcase mr-1"></i> Career</router-link
        >
        <router-link to="/gallery" class="nav-item border-b border-blue-800"
          ><i class="fas fa-images mr-1"></i> Gallery</router-link
        >
        <router-link to="/contact" class="nav-item border-b border-blue-800"
          ><i class="fas fa-envelope mr-1"></i> Contact</router-link
        >
        <router-link to="/dashboard" class="nav-item"
          ><i class="fas fa-user mr-1"></i> Login</router-link
        >
      </div>
    </div>
  </nav>
</template>

<script setup>
import {
  onMounted,
  onUnmounted,
  watch,
} from 'vue'

import { useRoute } from 'vue-router'

const route = useRoute();
const removeSticky = () => {
  const nav = document.querySelector(".sticky-nav");
  if (nav) nav.classList.remove("sticky-nav");
};

// Add scroll behavior for sticky navigation
onMounted(() => {
  window.addEventListener("scroll", handleScroll);
  if (route.path === "/gallery") {
    removeSticky();
  }
});
// Watch for route changes to handle sticky removal
watch(
  () => route.path,
  (newPath) => {
    if (newPath === "/gallery") {
      removeSticky();
    }
  }
);

onUnmounted(() => {
  window.removeEventListener("scroll", handleScroll);
});

const handleScroll = () => {
  const nav = document.querySelector(".sticky-nav");

  if (window.scrollY > 100) {
    // Start sticking after scrolling 100px
    nav.classList.add("is-sticky");
  } else {
    nav.classList.remove("is-sticky");
  }
};
</script>

<style>
/* Existing styles remain unchanged */

/* Sticky navigation styles */
.sticky-nav {
  position: sticky;
  top: 0;
  width: 100%;
  z-index: 1000;
  transition: all 0.3s ease;
}

.sticky-nav.is-sticky {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Ensure dropdowns appear above other content */
.sticky-nav .group-hover\:block {
  z-index: 1001;
}
</style>
