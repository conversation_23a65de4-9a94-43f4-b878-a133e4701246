<template>
  <section class="download-forms py-12 bg-white">
    <div class="container mx-auto px-4">
      <!-- Section Header -->
      <div class="mb-10 text-center">
        <h2 class="text-2xl md:text-3xl font-bold text-blue-800">Download Forms</h2>
      </div>

      <!-- Forms Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Form Card (Dynamic) -->
        <div
          v-for="(form, index) in formsData"
          :key="index"
          class="form-card text-center"
        >
          <div class="mb-4">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-6 w-6 mx-auto text-blue-800"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                :d="defaultIconPath"
              />
            </svg>
          </div>
          <h3 class="text-sm font-medium text-blue-800 mb-3">{{ form.docName }}</h3>
            <a
            :href="form.docUrl"
            target="_blank"
            rel="noopener noreferrer"
            class="inline-block bg-blue-600 hover:bg-blue-700 text-white text-xs font-medium py-1.5 px-4 rounded transition-colors duration-300"
            :download="form.docName"
            >
            Download
            </a>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import {
  onMounted,
  ref,
} from 'vue'

import axios from 'axios'

import api from '@/api/api'
import { ORG_ID } from '@/api/endpoint'

const defaultIconPath =
  "M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z";

const formsData = ref([]);

const fetchFormsData = async () => {
  try {
    const response = await api.get(`/customer/webComponent/${ORG_ID}/download-forms`);
    // Only keep the first 4 forms
    formsData.value = response.data.aboutUsDetails
      .flatMap((section) => section.aboutUsDocs)
      .slice(0, 4);
  } catch (error) {
    console.error("Error fetching forms data:", error);
    formsData.value = [];
  }
};

onMounted(() => {
  fetchFormsData();
  setTimeout(() => {
    const formCards = document.querySelectorAll(".form-card");

    // Staggered animation for form cards
    if (formCards.length) {
      formCards.forEach((card, index) => {
        setTimeout(() => {
          card.classList.add("opacity-100", "translate-y-0");
        }, 100 + index * 100);
      });
    }
  }, 300);
});
</script>

<style scoped>
.download-forms {
  position: relative;
}

.form-card {
  opacity: 0;
  transform: translateY(10px);
  transition: opacity 0.5s ease, transform 0.5s ease;
}

.form-card.opacity-100 {
  opacity: 1;
}

.form-card.translate-y-0 {
  transform: translateY(0);
}

.form-card:hover svg {
  transform: translateY(-2px);
}

.form-card svg {
  transition: transform 0.3s ease;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .form-card {
    margin-bottom: 1rem;
  }
}
</style>
