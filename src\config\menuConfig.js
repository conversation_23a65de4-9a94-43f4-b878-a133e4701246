/**
 * Sidebar Menu Configuration
 *
 * Structure:
 * {
 *   id: Unique identifier for the menu item
 *   type: 'title' for section headers, 'item' for regular menu items
 *   label: Display text for the menu item
 *   icon: Icon class (using Unicons or other icon libraries)
 *   route: Route path for navigation (optional)
 *   exact: Whether the route should match exactly for highlighting (default: true)
 *   badge: Badge to display next to the menu item (optional)
 *   children: Array of submenu items (optional)
 * }
 */

/**
 * Helper function to create a menu item
 * @param {string} id - Unique identifier
 * @param {string} label - Display text
 * @param {string} icon - Icon class (optional)
 * @param {string} route - Route path (optional)
 * @param {boolean} exact - Whether route should match exactly (optional, default: true)
 * @param {Object} badge - Badge object (optional) { text, variant }
 * @param {Array} children - Submenu items (optional)
 * @returns {Object} Menu item object
 */
const createMenuItem = (id, label, { icon, route, exact = true, badge, children, type = 'item' } = {}) => ({
  id,
  type,
  label,
  ...(icon && { icon }),
  ...(route && { route }),
  ...(exact === false && { exact }),
  ...(badge && { badge }),
  ...(children && { children })
})

/**
 * Create a section title
 * @param {string} id - Unique identifier
 * @param {string} label - Display text
 * @returns {Object} Title object
 */
const createTitle = (id, label) => createMenuItem(id, label, { type: 'title' })

export const menuItems = [
  createMenuItem('dashboard', 'Dashboard', {
    icon: 'uil uil-create-dashboard',
    route: '/'
  }),

  // createMenuItem('apps', 'Applications', {
  //   icon: 'uil uil-apps',
  //   children: [
  //     createMenuItem('email', 'Email', {
  //       icon: 'uil uil-envelope',
  //       route: '/apps/email'
  //     }),
  //     createMenuItem('chat', 'Chat', {
  //       icon: 'uil uil-comments',
  //       route: '/apps/chat'
  //     }),
  //     createMenuItem('calendar', 'Calendar', {
  //       icon: 'uil uil-calendar-alt',
  //       route: '/apps/calendar'
  //     })
  //   ]
  // }),

  // createMenuItem('ui', 'UI Elements', {
  //   icon: 'uil uil-layers',
  //   children: [
  //     createMenuItem('components', 'Components', {
  //       icon: 'uil uil-object-ungroup',
  //       children: [
  //         createMenuItem('alerts', 'Alerts', {
  //           route: '/ui/components/alerts'
  //         }),
  //         createMenuItem('buttons', 'Buttons', {
  //           route: '/ui/components/buttons'
  //         }),
  //         createMenuItem('cards', 'Cards', {
  //           route: '/ui/components/cards'
  //         })
  //       ]
  //     }),
  //     createMenuItem('forms', 'Forms', {
  //       icon: 'uil uil-document-layout-left',
  //       route: '/ui/forms'
  //     }),
  //     createMenuItem('tables', 'Tables', {
  //       icon: 'uil uil-table',
  //       route: '/ui/tables'
  //     })
  //   ]
  // }),

  // createTitle('pages', 'Pages'),

  // createMenuItem('authentication', 'Authentication', {
  //   icon: 'uil uil-lock',
  //   children: [
  //     createMenuItem('login', 'Login', {
  //       route: '/auth/login'
  //     }),
  //     createMenuItem('register', 'Register', {
  //       route: '/auth/register'
  //     }),
  //     createMenuItem('forgot-password', 'Forgot Password', {
  //       route: '/auth/forgot-password'
  //     })
  //   ]
  // }),

  // createMenuItem('settings', 'Settings', {
  //   icon: 'uil uil-setting',
  //   route: '/settings'
  // }),

  createMenuItem('about', 'About', {
    icon: 'uil uil-info-circle',
    route: '/about',
    badge: {
      text: 'New',
      variant: 'primary'
    }
  }),

  // createMenuItem('validation', 'Form Validation', {
  //   icon: 'uil uil-check-circle',
  //   route: '/validation',
  //   badge: {
  //     text: 'New',
  //     variant: 'success'
  //   }
  // }),

  // createMenuItem('simple-validation', 'Simple Validation', {
  //   icon: 'uil uil-shield-check',
  //   route: 'simple-validation',
  //   badge: {
  //     text: 'Unique',
  //     variant: 'primary'
  //   }
  // }),

  // createMenuItem('direct-validation', 'Direct HTML Validation', {
  //   icon: 'uil uil-bolt',
  //   route: 'direct-validation',
  //   badge: {
  //     text: 'Fast',
  //     variant: 'success'
  //   }
  // }),

  // createMenuItem('data-tables', 'Data Tables', {
  //   icon: 'uil uil-table',
  //   badge: {
  //     text: 'New',
  //     variant: 'primary'
  //   },
  //   children: [
  //     createMenuItem('data-table', 'Client-Side Table', {
  //       route: 'data-table',
  //       badge: {
  //         text: 'Basic',
  //         variant: 'info'
  //       }
  //     }),
  //     createMenuItem('api-table', 'API Table with Pagination', {
  //       route: 'api-table',
  //       badge: {
  //         text: 'Server',
  //         variant: 'success'
  //       }
  //     }),
  //     createMenuItem('admissions-table', 'Admissions API Example', {
  //       route: 'admissions-table',
  //       badge: {
  //         text: 'Laravel',
  //         variant: 'danger'
  //       }
  //     })
  //   ]
  // })
]
