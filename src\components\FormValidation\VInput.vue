<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { validateValue } from '@/utils/validation'

const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: ''
  },
  label: {
    type: String,
    default: ''
  },
  name: {
    type: String,
    default: ''
  },
  type: {
    type: String,
    default: 'text'
  },
  placeholder: {
    type: String,
    default: ''
  },
  validations: {
    type: Array,
    default: () => []
  },
  formValues: {
    type: Object,
    default: () => ({})
  },
  validateOnBlur: {
    type: Boolean,
    default: true
  },
  validateOnChange: {
    type: <PERSON>olean,
    default: false
  },
  validateOnMount: {
    type: Boolean,
    default: false
  },
  customClass: {
    type: String,
    default: ''
  },
  size: {
    type: String,
    default: 'medium', // small, medium, large
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  },
  disabled: {
    type: Boolean,
    default: false
  },
  readonly: {
    type: Boolean,
    default: false
  },
  icon: {
    type: String,
    default: ''
  },
  iconPosition: {
    type: String,
    default: 'left',
    validator: (value) => ['left', 'right'].includes(value)
  }
})

const emit = defineEmits(['update:modelValue', 'validation', 'blur', 'focus', 'input'])

const inputValue = computed({
  get: () => props.modelValue,
  set: (value) => {
    emit('update:modelValue', value)
    if (props.validateOnChange) {
      validate()
    }
  }
})

const errors = ref([])
const touched = ref(false)
const validating = ref(false)

const isValid = computed(() => errors.value.length === 0)
const showError = computed(() => touched.value && !isValid.value)

const sizeClass = computed(() => {
  switch (props.size) {
    case 'small': return 'ih-small'
    case 'large': return 'ih-large'
    default: return 'ih-medium'
  }
})

const inputClasses = computed(() => {
  return {
    'form-control': true,
    'is-invalid': showError.value,
    'is-valid': touched.value && isValid.value,
    [sizeClass.value]: true,
    [props.customClass]: !!props.customClass,
    'disabled': props.disabled
  }
})

const wrapperClasses = computed(() => {
  return {
    'form-group': true,
    'with-icon': !!props.icon,
    [`icon-${props.iconPosition}`]: !!props.icon
  }
})

const validate = async () => {
  validating.value = true
  const { errors: validationErrors } = await validateValue(
    inputValue.value,
    props.validations,
    props.formValues
  )
  errors.value = validationErrors
  emit('validation', { valid: isValid.value, errors: errors.value, field: props.name })
  validating.value = false
  return isValid.value
}

const handleBlur = (event) => {
  touched.value = true
  if (props.validateOnBlur) {
    validate()
  }
  emit('blur', event)
}

const handleFocus = (event) => {
  emit('focus', event)
}

const handleInput = (event) => {
  emit('input', event)
}

watch(() => props.formValues, () => {
  if (touched.value) {
    validate()
  }
}, { deep: true })

onMounted(() => {
  if (props.validateOnMount) {
    touched.value = true
    validate()
  }
})

defineExpose({ validate, errors, isValid })
</script>

<template>
  <div :class="wrapperClasses">
    <label v-if="label" :for="name">{{ label }}</label>
    
    <div class="input-container" :class="{ 'icon-left': icon && iconPosition === 'left', 'icon-right': icon && iconPosition === 'right' }">
      <span v-if="icon" class="input-icon" :class="`icon-${iconPosition}`">
        <i :class="icon"></i>
      </span>
      
      <input
        :id="name"
        :name="name"
        :type="type"
        :class="inputClasses"
        :placeholder="placeholder"
        v-model="inputValue"
        :disabled="disabled"
        :readonly="readonly"
        @blur="handleBlur"
        @focus="handleFocus"
        @input="handleInput"
      />
    </div>
    
    <div v-if="showError" class="invalid-feedback">
      {{ errors[0] }}
    </div>
  </div>
</template>

<style scoped>
.form-group {
  margin-bottom: 1.5rem;
}

.input-container {
  position: relative;
}

.input-icon {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
}

.icon-left .input-icon {
  left: 15px;
}

.icon-right .input-icon {
  right: 15px;
}

.icon-left input {
  padding-left: 40px;
}

.icon-right input {
  padding-right: 40px;
}
</style>
