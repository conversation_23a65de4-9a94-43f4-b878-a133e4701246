<script setup>
import { ref } from 'vue'

import { rules } from '@/utils/validation'

import {
  VCheckbox,
  VForm,
  VInput,
  VRadio,
  VSelect,
  VTextarea,
} from './index'

const formData = ref({
  name: '',
  email: '',
  password: '',
  confirmPassword: '',
  age: '',
  gender: '',
  interests: [],
  subscription: false,
  message: '',
  country: ''
})

const countries = [
  { value: 'us', label: 'United States' },
  { value: 'ca', label: 'Canada' },
  { value: 'uk', label: 'United Kingdom' },
  { value: 'au', label: 'Australia' }
]

const genderOptions = [
  { value: 'male', label: 'Male' },
  { value: 'female', label: 'Female' },
  { value: 'other', label: 'Other' }
]

const interestOptions = [
  { value: 'sports', label: 'Sports' },
  { value: 'music', label: 'Music' },
  { value: 'movies', label: 'Movies' },
  { value: 'reading', label: 'Reading' }
]

// Custom validation rule for password strength
const isStrongPassword = (value) => {
  if (!value) return true // Skip if empty

  const hasUpperCase = /[A-Z]/.test(value)
  const hasLowerCase = /[a-z]/.test(value)
  const hasNumbers = /\d/.test(value)
  const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(value)

  return (
    hasUpperCase &&
    hasLowerCase &&
    hasNumbers &&
    hasSpecialChar
  ) || 'Password must include uppercase, lowercase, number, and special character'
}

const validationRules = {
  name: [rules.required, rules.minLength(3)],
  email: [rules.required, rules.email],
  password: [rules.required, rules.minLength(8), isStrongPassword],
  confirmPassword: [rules.required, rules.match('password', 'Password')],
  age: [rules.numeric, rules.min(18)],
  gender: [rules.required],
  interests: [(value) => value.length > 0 || 'Please select at least one interest'],
  message: [rules.maxLength(500)],
  country: [rules.required]
}

const handleSubmit = ({ values, valid }) => {
  if (valid) {
    // Process form submission
    console.log('Form submitted:', values)

    // Show success notification
    if (window.toastr) {
      window.toastr.success('Form submitted successfully!', 'Success', {
        position: 'top-right',
        duration: 5000
      })
    } else {
      alert('Form submitted successfully!')
    }
  } else {
    // Show error notification
    if (window.toastr) {
      window.toastr.error('Please fix the errors in the form', 'Error', {
        position: 'top-right',
        duration: 5000
      })
    } else {
      alert('Please fix the errors in the form')
    }
  }
}
</script>

<template>
  <div class="card">
    <div class="card-header">
      <h5>Example Validation Form</h5>
    </div>
    <div class="card-body">
      <VForm :initial-values="formData" :validation-rules="validationRules" @submit="handleSubmit">
        <template #default="{ values, errors, isSubmitting }">
          <div class="row">
            <div class="col-md-6">
              <VInput v-model="values.name" name="name" label="Name" placeholder="Enter your name"
                :validations="validationRules.name" :form-values="values" icon="uil uil-user" />
            </div>

            <div class="col-md-6">
              <VInput v-model="values.email" name="email" label="Email" type="email" placeholder="Enter your email"
                :validations="validationRules.email" :form-values="values" icon="uil uil-envelope" />
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <VInput v-model="values.password" name="password" label="Password" type="password"
                placeholder="Enter your password" :validations="validationRules.password" :form-values="values"
                icon="uil uil-lock" />
            </div>

            <div class="col-md-6">
              <VInput v-model="values.confirmPassword" name="confirmPassword" label="Confirm Password" type="password"
                placeholder="Confirm your password" :validations="validationRules.confirmPassword" :form-values="values"
                icon="uil uil-lock" />
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <VInput v-model="values.age" name="age" label="Age" type="number" placeholder="Enter your age"
                :validations="validationRules.age" :form-values="values" icon="uil uil-calendar-alt" />
            </div>

            <div class="col-md-6">
              <VSelect v-model="values.country" name="country" label="Country" :options="countries"
                :validations="validationRules.country" :form-values="values" />
            </div>
          </div>

          <div class="row">
            <div class="col-md-12">
              <label>Gender</label>
              <div class="d-flex gap-4 mb-3">
                <VRadio v-for="option in genderOptions" :key="option.value" v-model="values.gender" name="gender"
                  :label="option.label" :value="option.value" :validations="validationRules.gender"
                  :form-values="values" />
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-12">
              <label>Interests</label>
              <div class="d-flex flex-wrap gap-4 mb-3">
                <VCheckbox v-for="option in interestOptions" :key="option.value" v-model="values.interests"
                  name="interests" :label="option.label" :value="option.value" :validations="validationRules.interests"
                  :form-values="values" />
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-12">
              <VCheckbox v-model="values.subscription" name="subscription" label="Subscribe to newsletter"
                :form-values="values" />
            </div>
          </div>

          <div class="row">
            <div class="col-md-12">
              <VTextarea v-model="values.message" name="message" label="Message (optional)"
                placeholder="Enter your message" :validations="validationRules.message" :form-values="values"
                rows="5" />
            </div>
          </div>

          <div class="row">
            <div class="col-md-12">
              <button type="submit" class="btn btn-primary" :disabled="isSubmitting">
                {{ isSubmitting ? 'Submitting...' : 'Submit' }}
              </button>
              <button type="reset" class="btn btn-light ms-2">Reset</button>
            </div>
          </div>
        </template>
      </VForm>
    </div>
  </div>
</template>
