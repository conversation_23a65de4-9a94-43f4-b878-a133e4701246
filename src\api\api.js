import axios from 'axios'

// Create Axios instance
const api = axios.create({
    baseURL: import.meta.env.VITE_API_URL || 'http://************:8080/WaterTariffSystem-web/webresources',
    headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Basic ' + btoa('admin:admin')
    }
})

// Flag to prevent multiple refresh calls
let isRefreshing = false
let failedQueue = []

// Handle queued requests waiting for token refresh
const processQueue = (error, newToken = null) => {
    failedQueue.forEach(prom => {
        if (error) {
            prom.reject(error)
        } else {
            prom.resolve(newToken)
        }
    })
    failedQueue = []
}

// 🔐 REQUEST INTERCEPTOR

api.interceptors.request.use(async config => {
    const user = JSON.parse(localStorage.getItem('user'))

    // If no user, just return config
    if (!user) return config
    const now = new Date()
    const accessExp = new Date(user.access_expires_at)
    const refreshExp = new Date(user.refresh_expires_at)

    // CASE 1: If Access Token is expired, but Refresh Token still valid
    if (now >= accessExp && now < refreshExp) {
        if (!isRefreshing) {
            isRefreshing = true
            try {
                const res = await axios.post(`${api.defaults.baseURL}/auth/refresh-token`, {
                    refreshToken: user.refresh_token
                })

                const newData = {
                    ...user,
                    access_token: res.data.accessToken.token,
                    access_expires_at: res.data.accessToken.expiresIn,
                    // Keep the original refresh token and its expiry date
                    // refresh_token: res.data.refreshToken.token,
                    // refresh_expires_at: res.data.refreshToken.expiresIn,
                }

                localStorage.setItem('user', JSON.stringify(newData))
                config.headers.Authorization = `Bearer ${newData.access_token}`

                isRefreshing = false
                processQueue(null, newData.access_token)

                return config
            } catch (error) {
                isRefreshing = false
                processQueue(error, null)
                alert('Session expired. Please log in again.');
                localStorage.removeItem('user')
                window.location.href = '/login'
                return Promise.reject(error)
            }
        }

        // Push current request into queue
        return new Promise((resolve, reject) => {
            failedQueue.push({
                resolve: token => {
                    config.headers.Authorization = `Bearer ${token}`
                    resolve(config)
                },
                reject: err => reject(err)
            })
        })
    }

    // CASE 2: Access token is still valid
    if (now < accessExp) {
        config.headers.Authorization = `Bearer ${user.access_token}`
    }
    console.log(now, '   ', refreshExp);
    // CASE 3: Both tokens expired — force logout
    if (now.getTime() >= refreshExp.getTime()) {
        localStorage.removeItem('user')
        toastr.error('Session expired. Please login again.!', 'Error', {
            position: 'top-right',
            duration: 5000
        });
        window.location.href = '/login'
        return Promise.reject(new Error('Session expired. Please login again.'))
    }

    return config
}, error => {
    return Promise.reject(error)
})

// 🔴 GLOBAL ERROR HANDLER
api.interceptors.response.use(
    response => response,
    error => {
        const user = JSON.parse(localStorage.getItem('user'))

        // Only proceed with logout if user exists and it's a 401 error
        if (error.response && error.response.status === 401 && user) {
            // Check if we're already refreshing tokens
            if (isRefreshing) {
                return Promise.reject(error)
            }

            const now = new Date()
            const refreshExp = new Date(user.refresh_expires_at)

            // Only logout if refresh token is expired
            if (now >= refreshExp) {
                localStorage.removeItem('user')
                toastr.error('Session expired. Please login again.!', 'Error', {
                    position: 'top-right',
                    duration: 5000
                });
                window.location.href = '/login'
            }
            // Otherwise, let request interceptor handle the refresh
        }
        return Promise.reject(error)
    }
)

export default api
