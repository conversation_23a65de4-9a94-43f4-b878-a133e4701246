<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Water Supply Management Board</title>

    <!-- Tailwind CSS via CDN -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Custom Tailwind Configuration -->
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: "#001755", // dark blue (navigation color)
              secondary: "#0ea5e9", // light blue
              accent: "#f59e0b", // amber
            },
          },
        },
      };
    </script>

    <!-- Custom CSS -->
    <style type="text/tailwindcss">
      @layer components {
        .nav-item {
          @apply px-4 py-2 text-white hover:bg-blue-800 transition duration-300;
        }
        .slider-dot {
          @apply w-3 h-3 rounded-full bg-gray-300 mx-1 cursor-pointer;
        }
        .slider-dot.active {
          @apply bg-blue-600;
        }
      }
    </style>

    <!-- Font Awesome for icons -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />

    <!-- Custom additional styles -->
    <style>
      .marquee {
        white-space: nowrap;
        overflow: hidden;
      }
      .marquee span {
        display: inline-block;
        padding-left: 100%;
        animation: marquee 30s linear infinite;
      }
      @keyframes marquee {
        0% {
          transform: translate(0, 0);
        }
        100% {
          transform: translate(-100%, 0);
        }
      }
    </style>

    <!-- External CSS -->
    <link rel="stylesheet" href="css/styles.css" />
  </head>
  <body class="bg-gray-100">
    <!-- Component Containers -->
    <div id="header-container"></div>
    <div id="nav-container"></div>
    <div id="slider-container"></div>
    <div id="whats-new-container"></div>
    <div id="main-content-container"></div>
    <div id="footer-container"></div>

    <!-- JavaScript -->
    <script src="js/components.js"></script>
    <script src="js/main.js"></script>
  </body>
</html>
