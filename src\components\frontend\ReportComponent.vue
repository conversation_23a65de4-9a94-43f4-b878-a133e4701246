<template>
  <section class="report-component py-8">
    <div class="container mx-auto px-4">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <!-- Register Complaints Card -->
        <div
          class="report-card transform transition-all duration-300 hover:-translate-y-1 hover:shadow-lg"
        >
          <router-link
            to="/contact"
            class="block bg-gradient-to-r from-blue-800 to-blue-600 rounded-lg overflow-hidden"
          >
            <div class="p-6 text-center">
              <div class="flex justify-center mb-3">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-8 w-8 text-white"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
              </div>
              <h3 class="text-lg font-medium text-white">Register your complaints</h3>
            </div>
          </router-link>
        </div>

        <!-- Water Quality Test Report Card -->
        <div
          class="report-card transform transition-all duration-300 hover:-translate-y-1 hover:shadow-lg"
        >
          <router-link
            to="publications/water-quality-test-report"
            class="block bg-gradient-to-r from-blue-800 to-blue-600 rounded-lg overflow-hidden"
          >
            <div class="p-6 text-center">
              <div class="flex justify-center mb-3">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-8 w-8 text-white"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"
                  />
                </svg>
              </div>
              <h3 class="text-lg font-medium text-white">Water Quality Test Report</h3>
            </div>
          </router-link>
        </div>

        <!-- Annual Report Card -->
        <div
          class="report-card transform transition-all duration-300 hover:-translate-y-1 hover:shadow-lg"
        >
          <router-link
            to="/publications/annual-reports"
            class="block bg-gradient-to-r from-blue-800 to-blue-600 rounded-lg overflow-hidden"
          >
            <div class="p-6 text-center">
              <div class="flex justify-center mb-3">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-8 w-8 text-white"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
              </div>
              <h3 class="text-lg font-medium text-white">Annual Report</h3>
            </div>
          </router-link>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
// Component is fully static, no script logic needed
</script>

<style scoped>
.report-component {
  position: relative;
}

.report-card {
  position: relative;
  z-index: 1;
}

.report-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  z-index: -1;
  transform: scale(0.95);
  opacity: 0;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
}

.report-card:hover::before {
  transform: scale(1);
  opacity: 1;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .report-card {
    margin-bottom: 1rem;
  }
}
</style>
