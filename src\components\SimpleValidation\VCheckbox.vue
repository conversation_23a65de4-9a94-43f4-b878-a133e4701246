<script setup>
import {
  computed,
  ref,
} from 'vue'

const props = defineProps({
  field: {
    type: Object,
    required: true
  },
  label: {
    type: String,
    default: ''
  },
  id: {
    type: String,
    default: ''
  },
  validateOnChange: {
    type: Boolean,
    default: false
  }
})

// Only show errors after the user has interacted with the field
const localTouched = ref(false)

const handleBlur = () => {
  localTouched.value = true
  props.field.setTouched()
}

const handleChange = (event) => {
  // For checkboxes, we want to validate only after the user has explicitly checked/unchecked
  // and not on the initial click
  if (props.validateOnChange) {
    // Delay setting touched to allow the model to update first
    setTimeout(() => {
      localTouched.value = true
      props.field.setTouched()
    }, 0)
  }
}

const classes = computed(() => ({
  'v-form-check': true,
  'has-error': localTouched.value && props.field.hasError.value
}))
</script>

<template>
  <div :class="classes">
    <input :id="id" type="checkbox" v-model="field.value" @blur="handleBlur" @change="handleChange" />
    <label v-if="label" :for="id">{{ label }}</label>
    <div v-if="localTouched && field.hasError.value" class="error-message">
      {{ field.errors.value[0] }}
    </div>
  </div>
</template>
