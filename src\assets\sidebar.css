/* Custom sidebar styles */

/* Nested menu indentation */
.sidebar__menu-group ul.sidebar_nav li.has-child ul li a {
  padding-left: 56px; /* Increased indentation for second level */
}

.sidebar__menu-group ul.sidebar_nav li.has-child ul li ul li a {
  padding-left: 76px; /* Increased indentation for third level */
}

/* Active menu item highlighting */
.sidebar__menu-group ul.sidebar_nav li.active {
  background-color: rgba(var(--color-primary-rgba), 0.10);
}

.sidebar__menu-group ul.sidebar_nav li.has-child ul li.active {
  background-color: rgba(var(--color-primary-rgba), 0.05);
  border-radius: 0 50rem 50rem 0;
}

/* Badge styles */
.menuItem.primary {
  background-color: var(--color-primary);
  color: white;
}

.menuItem.success {
  background-color: var(--color-success);
  color: white;
}

.menuItem.warning {
  background-color: var(--color-warning);
  color: white;
}

.menuItem.danger {
  background-color: var(--color-danger);
  color: white;
}

/* Smooth transitions */
.sidebar__menu-group ul.sidebar_nav li ul {
  transition: all 0.3s ease;
}

/* Hover effect for menu items */
.sidebar__menu-group ul.sidebar_nav li > a:hover {
  background-color: rgba(var(--color-primary-rgba), 0.05);
}
