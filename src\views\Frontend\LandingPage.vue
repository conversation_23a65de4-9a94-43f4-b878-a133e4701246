<template>
  <!-- Slider Component -->
  <Slider />
  <main class="container mx-auto px-4 py-8">
    <IntorductionComponent />
    <BannerAd />
  </main>
  <FunctionalComponent />
  <ReportComponent />
  <ComitteMember />
  <FormComponent />
</template>

<script setup>
import BannerAd from '@/components/frontend/BannerAd.vue'
import ComitteMember from '@/components/frontend/ComitteMember.vue'
import FormComponent from '@/components/frontend/FormComponent.vue'
import FunctionalComponent from '@/components/frontend/FunctionalComponent.vue'
import IntorductionComponent from '@/components/frontend/IntroductionComponent.vue'
import ReportComponent from '@/components/frontend/ReportComponent.vue'
import Slider from '@/components/frontend/Slider.vue'

const data = [
  {
    tabName: "home",
    components: [
      {
        name: "Functional Component",
        id: "01-functional-component",
      },
      {
        name: "Executive Comittee Members",
        id: "02-executive-comittee-members",
      },
      {
        name: "Download Forms",
        id: "03-download-forms",
      },
    ],
  },
  {
    tabName: "organization",
    components: [
      {
        name: "Executive Director Provisions",
        id: "04-executive-director-provisions",
      },
      {
        name: "Offences and Punishments",
        id: "05-offences-punishments",
      },
    ],
  },
  {
    tabName: "Miscellaneous",
    components: [
      {
        name: "Miscellaneous",
        id: "06-miscellaneous",
      },
    ],
  },
  {
    tabName: "Publications",
    components: [
      {
        name: "Annual Reports",
        id: "07-annual-reports",
      },
      {
        name: "Audit Reports",
        id: "08-audit-reports",
      },
      {
        name: "Water Quality Test Report",
        id: "09-water-quality-test-report",
      },
      {
        name: "Downloads",
        id: "10-downloads",
      },
    ],
  },
  {
    tabName: "Notices",
    components: [
      {
        name: "Tender Notices",
        id: "11-tender-notices",
      },
      {
        name: "Vacancy",
        id: "12-vacancy",
      },
      {
        name: "Tarrif and Others",
        id: "13-tariff-and-others",
      },
      {
        name: "Consumer Notice",
        id: "14-consumer-notice",
      },
    ],
  },
  {
    tabName: "Carrer",
    components: [
      {
        name: "Carrer",
        id: "15-carrer",
      },
    ],
  },
  {
    tabName: "Gallery",
    components: [
      {
        name: "Photo Gallery",
        id: "16-gallery",
      },
    ],
  },
  {
    tabName: "Contact",
    components: [
      {
        name: "Contact Us",
        id: "17-contact",
      },
    ],
  },
];
</script>

<style scoped>
/* First letter styling for paragraph */
.first-letter\:text-4xl:first-letter {
  font-size: 2.25rem;
}

.first-letter\:font-bold:first-letter {
  font-weight: 700;
}

.first-letter\:text-blue-600:first-letter {
  color: #2563eb;
}

.first-letter\:mr-1:first-letter {
  margin-right: 0.25rem;
}

.first-letter\:float-left:first-letter {
  float: left;
}
</style>
