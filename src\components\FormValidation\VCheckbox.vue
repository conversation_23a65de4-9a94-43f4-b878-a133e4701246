<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { validateValue } from '@/utils/validation'

const props = defineProps({
  modelValue: {
    type: [Boolean, Array],
    default: false
  },
  label: {
    type: String,
    default: ''
  },
  name: {
    type: String,
    default: ''
  },
  value: {
    type: [String, Number, Boolean],
    default: true
  },
  validations: {
    type: Array,
    default: () => []
  },
  formValues: {
    type: Object,
    default: () => ({})
  },
  validateOnChange: {
    type: Boolean,
    default: true
  },
  validateOnMount: {
    type: Boolean,
    default: false
  },
  customClass: {
    type: String,
    default: ''
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'validation', 'change'])

const isChecked = computed({
  get: () => {
    if (Array.isArray(props.modelValue)) {
      return props.modelValue.includes(props.value)
    }
    return props.modelValue
  },
  set: (checked) => {
    let updatedValue
    
    if (Array.isArray(props.modelValue)) {
      if (checked) {
        updatedValue = [...props.modelValue, props.value]
      } else {
        updatedValue = props.modelValue.filter(item => item !== props.value)
      }
    } else {
      updatedValue = checked
    }
    
    emit('update:modelValue', updatedValue)
    if (props.validateOnChange) {
      validate()
    }
  }
})

const errors = ref([])
const touched = ref(false)
const validating = ref(false)

const isValid = computed(() => errors.value.length === 0)
const showError = computed(() => touched.value && !isValid.value)

const checkboxClasses = computed(() => {
  return {
    'form-check-input': true,
    'is-invalid': showError.value,
    'is-valid': touched.value && isValid.value,
    [props.customClass]: !!props.customClass,
    'disabled': props.disabled
  }
})

const validate = async () => {
  validating.value = true
  const { errors: validationErrors } = await validateValue(
    props.modelValue,
    props.validations,
    props.formValues
  )
  errors.value = validationErrors
  emit('validation', { valid: isValid.value, errors: errors.value, field: props.name })
  validating.value = false
  return isValid.value
}

const handleChange = (event) => {
  touched.value = true
  emit('change', event)
}

watch(() => props.formValues, () => {
  if (touched.value) {
    validate()
  }
}, { deep: true })

onMounted(() => {
  if (props.validateOnMount) {
    touched.value = true
    validate()
  }
})

defineExpose({ validate, errors, isValid })
</script>

<template>
  <div class="form-check">
    <input
      :id="name"
      :name="name"
      type="checkbox"
      :class="checkboxClasses"
      :value="value"
      v-model="isChecked"
      :disabled="disabled"
      @change="handleChange"
    />
    <label v-if="label" class="form-check-label" :for="name">
      {{ label }}
    </label>
    
    <div v-if="showError" class="invalid-feedback d-block">
      {{ errors[0] }}
    </div>
  </div>
</template>

<style scoped>
.form-check {
  margin-bottom: 1rem;
}

.invalid-feedback.d-block {
  display: block;
}
</style>
