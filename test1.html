<html lang="en" class="scroll-smooth">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Animated Solar System</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link
    rel="stylesheet"
    href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css"
  />
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@300;400;500;700&display=swap');

    body {
      font-family: 'Orbitron', monospace;
      background: radial-gradient(ellipse at center, #1a1a2e 0%, #16213e 25%, #0f0f23 50%, #000000 100%);
      overflow-x: hidden;
      min-height: 100vh;
      margin: 0;
      padding: 0;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    /* Enhanced starry background with nebula */
    .stars {
      position: fixed;
      width: 100vw;
      height: 100vh;
      top: 0;
      left: 0;
      z-index: 0;
      pointer-events: none;
      background:
        radial-gradient(2px 2px at 20px 30px, #eee, transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
        radial-gradient(1px 1px at 90px 40px, #fff, transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent),
        radial-gradient(2px 2px at 160px 30px, #ddd, transparent);
      background-repeat: repeat;
      background-size: 200px 100px;
    }

    .star {
      position: absolute;
      background: white;
      border-radius: 50%;
      opacity: 0.8;
      animation: twinkle 3s infinite alternate;
    }

    .nebula {
      position: absolute;
      border-radius: 50%;
      filter: blur(40px);
      opacity: 0.1;
      animation: nebulaDrift 20s infinite linear;
    }

    @keyframes twinkle {
      0% {
        opacity: 0.3;
        transform: scale(1);
      }
      50% {
        opacity: 1;
        transform: scale(1.2);
      }
      100% {
        opacity: 0.3;
        transform: scale(1);
      }
    }

    @keyframes nebulaDrift {
      0% {
        transform: rotate(0deg) translateX(100px) rotate(0deg);
      }
      100% {
        transform: rotate(360deg) translateX(100px) rotate(-360deg);
      }
    }

    /* Solar system container */
    .solar-system {
      position: relative;
      width: 98vmin;
      height: 98vmin;
      max-width: 1200px;
      max-height: 1200px;
      min-width: 400px;
      min-height: 400px;
      z-index: 10;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    /* Enhanced Sun */
    .sun {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 8vmin;
      height: 8vmin;
      max-width: 80px;
      max-height: 80px;
      min-width: 45px;
      min-height: 45px;
      transform: translate(-50%, -50%);
      background:
        radial-gradient(circle at 30% 30%, #fff8dc, #ffeb3b 20%, #ff9800 40%, #ff5722 70%, #d84315);
      border-radius: 50%;
      box-shadow:
        0 0 25px 10px rgba(255, 235, 59, 0.8),
        0 0 50px 20px rgba(255, 152, 0, 0.6),
        0 0 75px 30px rgba(255, 87, 34, 0.4),
        0 0 100px 40px rgba(255, 193, 7, 0.2);
      animation: sunGlow 4s ease-in-out infinite alternate, sunRotate 20s linear infinite;
      z-index: 20;
    }

    .sun::before {
      content: '';
      position: absolute;
      top: -10%;
      left: -10%;
      right: -10%;
      bottom: -10%;
      background: radial-gradient(circle, rgba(255, 235, 59, 0.3) 0%, transparent 70%);
      border-radius: 50%;
      animation: coronaFlicker 3s ease-in-out infinite alternate;
    }

    @keyframes sunGlow {
      0% {
        box-shadow:
          0 0 25px 10px rgba(255, 235, 59, 0.8),
          0 0 50px 20px rgba(255, 152, 0, 0.6),
          0 0 75px 30px rgba(255, 87, 34, 0.4),
          0 0 100px 40px rgba(255, 193, 7, 0.2);
        transform: translate(-50%, -50%) scale(1);
      }
      100% {
        box-shadow:
          0 0 35px 15px rgba(255, 235, 59, 1),
          0 0 60px 25px rgba(255, 152, 0, 0.8),
          0 0 85px 35px rgba(255, 87, 34, 0.6),
          0 0 120px 50px rgba(255, 193, 7, 0.3);
        transform: translate(-50%, -50%) scale(1.05);
      }
    }

    @keyframes sunRotate {
      from { transform: translate(-50%, -50%) rotate(0deg); }
      to { transform: translate(-50%, -50%) rotate(360deg); }
    }

    @keyframes coronaFlicker {
      0% { opacity: 0.3; }
      100% { opacity: 0.7; }
    }

    /* Enhanced Orbit container */
    .orbit {
      position: absolute;
      top: 50%;
      left: 50%;
      border: 1px solid rgba(255, 255, 255, 0.08);
      border-radius: 50%;
      transform-origin: center center;
      animation-timing-function: linear;
      animation-iteration-count: infinite;
      pointer-events: none;
      box-shadow:
        inset 0 0 20px rgba(255, 255, 255, 0.02),
        0 0 20px rgba(255, 255, 255, 0.02);
    }

    /* Planet container to rotate */
    .planet-orbit {
      position: absolute;
      top: 50%;
      left: 50%;
      transform-origin: center center;
      animation-timing-function: linear;
      animation-iteration-count: infinite;
    }

    /* Enhanced Planet */
    .planet {
      position: absolute;
      top: 50%;
      left: 100%;
      border-radius: 50%;
      transform: translate(-50%, -50%);
      display: flex;
      justify-content: center;
      align-items: center;
      color: white;
      font-size: 0.6rem;
      font-weight: 400;
      text-shadow: 0 0 3px rgba(0,0,0,0.9);
      user-select: none;
      position: relative;
      overflow: hidden;
    }

    .planet::before {
      content: '';
      position: absolute;
      top: -20%;
      left: -20%;
      width: 40%;
      height: 40%;
      background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%);
      border-radius: 50%;
      animation: planetHighlight 8s ease-in-out infinite alternate;
    }

    @keyframes planetHighlight {
      0% {
        transform: translate(0%, 0%) scale(1);
        opacity: 0.3;
      }
      100% {
        transform: translate(20%, 20%) scale(1.2);
        opacity: 0.6;
      }
    }

    /* Enhanced Planet label */
    .planet-label {
      position: absolute;
      top: 50%;
      left: 100%;
      transform: translate(0.8rem, -50%);
      white-space: nowrap;
      color: rgba(255, 255, 255, 0.9);
      font-size: 0.7rem;
      font-weight: 300;
      text-shadow:
        0 0 4px rgba(0,0,0,0.8),
        0 0 8px rgba(0,0,0,0.6);
      user-select: none;
      letter-spacing: 0.5px;
      opacity: 0.8;
      transition: opacity 0.3s ease;
    }

    .planet:hover .planet-label {
      opacity: 1;
    }

    /* Enhanced Individual planet sizes and colors */
    .mercury {
      width: 1.5vmin;
      height: 1.5vmin;
      max-width: 15px;
      max-height: 15px;
      min-width: 8px;
      min-height: 8px;
      background:
        radial-gradient(circle at 30% 30%, #c7c5c5, #8c8a8a 40%, #5a5858 70%, #3d3b3b);
      box-shadow:
        0 0 8px 2px rgba(167, 159, 159, 0.6),
        inset -2px -2px 4px rgba(0,0,0,0.3);
    }

    .venus {
      width: 2vmin;
      height: 2vmin;
      max-width: 20px;
      max-height: 20px;
      min-width: 12px;
      min-height: 12px;
      background:
        radial-gradient(circle at 30% 30%, #ffc649, #ffb347 30%, #ff8c42 60%, #d4691a);
      box-shadow:
        0 0 10px 2px rgba(255, 198, 73, 0.7),
        inset -2px -2px 4px rgba(0,0,0,0.2);
    }

    .earth {
      width: 2.2vmin;
      height: 2.2vmin;
      max-width: 22px;
      max-height: 22px;
      min-width: 14px;
      min-height: 14px;
      background:
        radial-gradient(circle at 30% 30%, #87ceeb, #4682b4 25%, #228b22 45%, #006400 70%, #2f4f4f);
      box-shadow:
        0 0 12px 3px rgba(70, 130, 180, 0.8),
        inset -2px -2px 4px rgba(0,0,0,0.3);
      animation: earthRotate 10s linear infinite;
    }

    @keyframes earthRotate {
      from { background-position: 0% 50%; }
      to { background-position: 100% 50%; }
    }

    .mars {
      width: 1.8vmin;
      height: 1.8vmin;
      max-width: 18px;
      max-height: 18px;
      min-width: 10px;
      min-height: 10px;
      background:
        radial-gradient(circle at 30% 30%, #cd5c5c, #a0522d 40%, #8b4513 70%, #654321);
      box-shadow:
        0 0 10px 2px rgba(205, 92, 92, 0.7),
        inset -2px -2px 4px rgba(0,0,0,0.3);
    }

    .jupiter {
      width: 5.5vmin;
      height: 5.5vmin;
      max-width: 55px;
      max-height: 55px;
      min-width: 32px;
      min-height: 32px;
      background:
        linear-gradient(0deg, #d2691e 0%, #daa520 20%, #f4a460 40%, #daa520 60%, #cd853f 80%, #a0522d 100%);
      box-shadow:
        0 0 20px 5px rgba(218, 165, 32, 0.8),
        inset -3px -3px 6px rgba(0,0,0,0.3);
      animation: jupiterBands 15s linear infinite;
    }

    @keyframes jupiterBands {
      from { background-position: 0% 0%; }
      to { background-position: 100% 0%; }
    }

    .saturn {
      width: 4.8vmin;
      height: 4.8vmin;
      max-width: 48px;
      max-height: 48px;
      min-width: 28px;
      min-height: 28px;
      background:
        radial-gradient(circle at 30% 30%, #fad5a5, #deb887 40%, #d2b48c 70%, #bc9a6a);
      box-shadow:
        0 0 18px 4px rgba(250, 213, 165, 0.8),
        inset -3px -3px 6px rgba(0,0,0,0.2);
    }

    .saturn::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 220%;
      height: 2px;
      background: linear-gradient(90deg, transparent 0%, rgba(250, 213, 165, 0.6) 20%, rgba(250, 213, 165, 0.8) 50%, rgba(250, 213, 165, 0.6) 80%, transparent 100%);
      transform: translate(-50%, -50%);
      border-radius: 50%;
      box-shadow:
        0 0 0 1px rgba(250, 213, 165, 0.4),
        0 3px 0 1px rgba(250, 213, 165, 0.3),
        0 -3px 0 1px rgba(250, 213, 165, 0.3);
    }

    .uranus {
      width: 3.2vmin;
      height: 3.2vmin;
      max-width: 32px;
      max-height: 32px;
      min-width: 20px;
      min-height: 20px;
      background:
        radial-gradient(circle at 30% 30%, #40e0d0, #20b2aa 40%, #008b8b 70%, #006666);
      box-shadow:
        0 0 15px 3px rgba(64, 224, 208, 0.8),
        inset -2px -2px 4px rgba(0,0,0,0.3);
    }

    .neptune {
      width: 3vmin;
      height: 3vmin;
      max-width: 30px;
      max-height: 30px;
      min-width: 18px;
      min-height: 18px;
      background:
        radial-gradient(circle at 30% 30%, #4169e1, #0000cd 40%, #000080 70%, #191970);
      box-shadow:
        0 0 15px 3px rgba(65, 105, 225, 0.8),
        inset -2px -2px 4px rgba(0,0,0,0.3);
    }

    /* Enhanced Responsive scaling */
    @media (max-width: 768px) {
      .solar-system {
        width: 95vmin;
        height: 95vmin;
        min-width: 350px;
        min-height: 350px;
      }
      .planet-label {
        font-size: 0.65rem;
      }
      .sun {
        width: 7vmin;
        height: 7vmin;
        min-width: 40px;
        min-height: 40px;
      }
    }

    @media (max-width: 480px) {
      .solar-system {
        width: 92vmin;
        height: 92vmin;
        min-width: 320px;
        min-height: 320px;
      }
      .planet-label {
        font-size: 0.6rem;
      }
      .sun {
        width: 6vmin;
        height: 6vmin;
        min-width: 35px;
        min-height: 35px;
      }
    }

    @media (min-width: 1400px) {
      .solar-system {
        max-width: 1400px;
        max-height: 1400px;
      }
    }
  </style>
</head>
<body>
  <div class="stars" aria-hidden="true"></div>

  <main class="solar-system" role="main" aria-label="Animated solar system with orbiting planets">
    <div class="sun" aria-label="Sun"></div>

    <!-- Mercury -->
    <div
      class="planet-orbit orbit"
      style="width: 12vmin; height: 12vmin; margin-top: -6vmin; margin-left: -6vmin; animation: orbit-mercury 8s linear infinite;"
      aria-label="Mercury orbit"
    >
      <div class="planet mercury" aria-label="Mercury planet">
        <span class="planet-label">Mercury</span>
      </div>
    </div>

    <!-- Venus -->
    <div
      class="planet-orbit orbit"
      style="width: 16vmin; height: 16vmin; margin-top: -8vmin; margin-left: -8vmin; animation: orbit-venus 15s linear infinite;"
      aria-label="Venus orbit"
    >
      <div class="planet venus" aria-label="Venus planet">
        <span class="planet-label">Venus</span>
      </div>
    </div>

    <!-- Earth -->
    <div
      class="planet-orbit orbit"
      style="width: 20vmin; height: 20vmin; margin-top: -10vmin; margin-left: -10vmin; animation: orbit-earth 25s linear infinite;"
      aria-label="Earth orbit"
    >
      <div class="planet earth" aria-label="Earth planet">
        <span class="planet-label">Earth</span>
      </div>
    </div>

    <!-- Mars -->
    <div
      class="planet-orbit orbit"
      style="width: 25vmin; height: 25vmin; margin-top: -12.5vmin; margin-left: -12.5vmin; animation: orbit-mars 47s linear infinite;"
      aria-label="Mars orbit"
    >
      <div class="planet mars" aria-label="Mars planet">
        <span class="planet-label">Mars</span>
      </div>
    </div>

    <!-- Jupiter -->
    <div
      class="planet-orbit orbit"
      style="width: 34vmin; height: 34vmin; margin-top: -17vmin; margin-left: -17vmin; animation: orbit-jupiter 95s linear infinite;"
      aria-label="Jupiter orbit"
    >
      <div class="planet jupiter" aria-label="Jupiter planet">
        <span class="planet-label">Jupiter</span>
      </div>
    </div>

    <!-- Saturn -->
    <div
      class="planet-orbit orbit"
      style="width: 40vmin; height: 40vmin; margin-top: -20vmin; margin-left: -20vmin; animation: orbit-saturn 150s linear infinite;"
      aria-label="Saturn orbit"
    >
      <div class="planet saturn" aria-label="Saturn planet">
        <span class="planet-label">Saturn</span>
      </div>
    </div>

    <!-- Uranus -->
    <div
      class="planet-orbit orbit"
      style="width: 45vmin; height: 45vmin; margin-top: -22.5vmin; margin-left: -22.5vmin; animation: orbit-uranus 210s linear infinite;"
      aria-label="Uranus orbit"
    >
      <div class="planet uranus" aria-label="Uranus planet">
        <span class="planet-label">Uranus</span>
      </div>
    </div>

    <!-- Neptune -->
    <div
      class="planet-orbit orbit"
      style="width: 49vmin; height: 49vmin; margin-top: -24.5vmin; margin-left: -24.5vmin; animation: orbit-neptune 280s linear infinite;"
      aria-label="Neptune orbit"
    >
      <div class="planet neptune" aria-label="Neptune planet">
        <span class="planet-label">Neptune</span>
      </div>
    </div>
  </main>

  <script>
    // Generate enhanced stars and nebula for the background
    const starsContainer = document.querySelector('.stars');
    const starCount = 200;
    const nebulaCount = 3;

    // Create stars
    for (let i = 0; i < starCount; i++) {
      const star = document.createElement('div');
      star.classList.add('star');
      const size = Math.random() * 2 + 0.3;
      const brightness = Math.random();

      star.style.width = `${size}px`;
      star.style.height = `${size}px`;
      star.style.top = `${Math.random() * 100}%`;
      star.style.left = `${Math.random() * 100}%`;
      star.style.animationDuration = `${(Math.random() * 4 + 2).toFixed(2)}s`;
      star.style.animationDelay = `${(Math.random() * 5).toFixed(2)}s`;

      // Add color variation to stars
      if (brightness > 0.8) {
        star.style.background = '#ffffff';
        star.style.boxShadow = '0 0 6px #ffffff';
      } else if (brightness > 0.6) {
        star.style.background = '#ffffcc';
        star.style.boxShadow = '0 0 4px #ffffcc';
      } else if (brightness > 0.4) {
        star.style.background = '#ccccff';
        star.style.boxShadow = '0 0 3px #ccccff';
      } else {
        star.style.background = '#ffcccc';
        star.style.boxShadow = '0 0 2px #ffcccc';
      }

      starsContainer.appendChild(star);
    }

    // Create nebula clouds
    for (let i = 0; i < nebulaCount; i++) {
      const nebula = document.createElement('div');
      nebula.classList.add('nebula');
      const size = Math.random() * 300 + 100;
      const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57'];
      const color = colors[Math.floor(Math.random() * colors.length)];

      nebula.style.width = `${size}px`;
      nebula.style.height = `${size}px`;
      nebula.style.top = `${Math.random() * 100}%`;
      nebula.style.left = `${Math.random() * 100}%`;
      nebula.style.background = `radial-gradient(circle, ${color}22 0%, transparent 70%)`;
      nebula.style.animationDuration = `${(Math.random() * 30 + 20).toFixed(2)}s`;
      nebula.style.animationDelay = `${(Math.random() * 10).toFixed(2)}s`;

      starsContainer.appendChild(nebula);
    }

    // Add shooting stars occasionally
    function createShootingStar() {
      const shootingStar = document.createElement('div');
      shootingStar.style.position = 'absolute';
      shootingStar.style.width = '2px';
      shootingStar.style.height = '2px';
      shootingStar.style.background = '#ffffff';
      shootingStar.style.borderRadius = '50%';
      shootingStar.style.boxShadow = '0 0 6px #ffffff';
      shootingStar.style.top = `${Math.random() * 50}%`;
      shootingStar.style.left = '0%';
      shootingStar.style.animation = 'shootingStar 3s linear forwards';

      starsContainer.appendChild(shootingStar);

      setTimeout(() => {
        if (shootingStar.parentNode) {
          shootingStar.parentNode.removeChild(shootingStar);
        }
      }, 3000);
    }

    // Create shooting star every 10-20 seconds
    setInterval(createShootingStar, Math.random() * 10000 + 10000);
  </script>

  <style>
    @keyframes shootingStar {
      0% {
        transform: translateX(0) translateY(0);
        opacity: 0;
      }
      10% {
        opacity: 1;
      }
      90% {
        opacity: 1;
      }
      100% {
        transform: translateX(100vw) translateY(50vh);
        opacity: 0;
      }
    }
  </style>

  <style>
    /* Orbit animations */
    @keyframes orbit-mercury {
      from {
        transform: rotate(0deg);
      }
      to {
        transform: rotate(360deg);
      }
    }
    @keyframes orbit-venus {
      from {
        transform: rotate(0deg);
      }
      to {
        transform: rotate(360deg);
      }
    }
    @keyframes orbit-earth {
      from {
        transform: rotate(0deg);
      }
      to {
        transform: rotate(360deg);
      }
    }
    @keyframes orbit-mars {
      from {
        transform: rotate(0deg);
      }
      to {
        transform: rotate(360deg);
      }
    }
    @keyframes orbit-jupiter {
      from {
        transform: rotate(0deg);
      }
      to {
        transform: rotate(360deg);
      }
    }
    @keyframes orbit-saturn {
      from {
        transform: rotate(0deg);
      }
      to {
        transform: rotate(360deg);
      }
    }
    @keyframes orbit-uranus {
      from {
        transform: rotate(0deg);
      }
      to {
        transform: rotate(360deg);
      }
    }
    @keyframes orbit-neptune {
      from {
        transform: rotate(0deg);
      }
      to {
        transform: rotate(360deg);
      }
    }
  </style>
</body>
</html>