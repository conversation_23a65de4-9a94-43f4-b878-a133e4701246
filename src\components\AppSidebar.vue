<template>
  <div class="sidebar-wrapper">
    <div class="sidebar" :class="{ 'collapsed': isCollapsed }" id="sidebar">
      <div class="sidebar__menu-group">
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<script setup>
import {
  onBeforeUnmount,
  onMounted,
  ref,
} from 'vue'

// State
const isCollapsed = ref(false)

// Methods
const checkWindowSize = () => {
  if (window.innerWidth <= 991) {
    isCollapsed.value = true
  }
}

// Lifecycle hooks
onMounted(() => {
  // Check if sidebar is already collapsed (for responsive views)
  isCollapsed.value = document.querySelector('.sidebar')?.classList.contains('collapsed') || false

  // Listen for sidebar toggle events
  window.addEventListener('resize', checkWindowSize)
  checkWindowSize()
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', checkWindowSize)
})
</script>
