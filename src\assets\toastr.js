/**
 * CustomToastr - A beautiful, customizable toast notification system
 * 
 * <AUTHOR> Name
 * @version 1.0.0
 */
class CustomToastr {
    constructor(options = {}) {
      // Default options
      this.defaults = {
        position: 'top-right',
        duration: 5000,
        closeButton: true,
        progressBar: true,
        newestOnTop: true,
        preventDuplicates: false,
        escapeHtml: true,
        onShow: null,
        onHide: null,
        onClick: null
      };
  
      // Merge default options with user options
      this.options = { ...this.defaults, ...options };
  
      // Initialize container
      this.initializeContainer();
    }
  
    /**
     * Initialize the toast container
     */
    initializeContainer() {
      // Check if container already exists
      let container = document.querySelector(`.custom-toast-container.${this.options.position}`);
      
      // Create container if it doesn't exist
      if (!container) {
        container = document.createElement('div');
        container.className = `custom-toast-container ${this.options.position}`;
        document.body.appendChild(container);
      }
      
      this.container = container;
    }
  
    /**
     * Create a new toast notification
     * 
     * @param {string} type - The type of toast (success, error, info, warning)
     * @param {string} message - The message to display
     * @param {string} title - Optional title for the toast
     * @param {object} options - Optional override options for this specific toast
     * @returns {HTMLElement} The toast element
     */
    createToast(type, message, title = '', options = {}) {
      // Merge default options with toast-specific options
      const toastOptions = { ...this.options, ...options };
      
      // Create toast element
      const toast = document.createElement('div');
      toast.className = `custom-toast ${type}`;
      
      // Add close button if enabled
      if (toastOptions.closeButton) {
        const closeButton = document.createElement('button');
        closeButton.className = 'custom-toast-close';
        closeButton.setAttribute('aria-label', 'Close');
        closeButton.addEventListener('click', () => this.removeToast(toast));
        toast.appendChild(closeButton);
      }
      
      // Add title if provided
      if (title) {
        const titleElement = document.createElement('div');
        titleElement.className = 'custom-toast-title';
        titleElement.textContent = toastOptions.escapeHtml ? this.escapeHtml(title) : title;
        toast.appendChild(titleElement);
      }
      
      // Add message
      const messageElement = document.createElement('div');
      messageElement.className = 'custom-toast-message';
      messageElement.textContent = toastOptions.escapeHtml ? this.escapeHtml(message) : message;
      toast.appendChild(messageElement);
      
      // Add progress bar if enabled
      if (toastOptions.progressBar) {
        const progressContainer = document.createElement('div');
        progressContainer.className = 'custom-toast-progress';
        
        const progressBar = document.createElement('div');
        progressBar.className = 'custom-toast-progress-bar';
        
        progressContainer.appendChild(progressBar);
        toast.appendChild(progressContainer);
        
        // Animate progress bar
        if (toastOptions.duration > 0) {
          setTimeout(() => {
            progressBar.style.transition = `width ${toastOptions.duration}ms linear`;
            progressBar.style.width = '0%';
          }, 10);
        }
      }
      
      // Add click handler
      if (typeof toastOptions.onClick === 'function') {
        toast.addEventListener('click', (e) => {
          if (e.target !== toast.querySelector('.custom-toast-close')) {
            toastOptions.onClick.call(toast, e);
          }
        });
        toast.style.cursor = 'pointer';
      }
      
      // Add to container
      if (toastOptions.newestOnTop) {
        this.container.prepend(toast);
      } else {
        this.container.appendChild(toast);
      }
      
      // Trigger onShow callback
      if (typeof toastOptions.onShow === 'function') {
        toastOptions.onShow.call(toast);
      }
      
      // Show toast with animation
      setTimeout(() => {
        toast.classList.add('show');
      }, 10);
      
      // Auto-remove toast after duration
      if (toastOptions.duration > 0) {
        toast.hideTimeout = setTimeout(() => {
          this.removeToast(toast);
        }, toastOptions.duration);
      }
      
      return toast;
    }
  
    /**
     * Remove a toast with animation
     * 
     * @param {HTMLElement} toast - The toast element to remove
     */
    removeToast(toast) {
      // Clear the timeout if it exists
      if (toast.hideTimeout) {
        clearTimeout(toast.hideTimeout);
      }
      
      // Add hide class for animation
      toast.classList.add('hide');
      toast.classList.remove('show');
      
      // Trigger onHide callback
      if (typeof this.options.onHide === 'function') {
        this.options.onHide.call(toast);
      }
      
      // Remove from DOM after animation completes
      setTimeout(() => {
        if (toast.parentNode) {
          toast.parentNode.removeChild(toast);
        }
        
        // Remove container if it's empty
        if (this.container.children.length === 0) {
          this.container.parentNode.removeChild(this.container);
        }
      }, 300);
    }
  
    /**
     * Clear all toasts
     */
    clear() {
      const toasts = this.container.querySelectorAll('.custom-toast');
      toasts.forEach(toast => this.removeToast(toast));
    }
  
    /**
     * Show a success toast
     * 
     * @param {string} message - The message to display
     * @param {string} title - Optional title
     * @param {object} options - Optional override options
     * @returns {HTMLElement} The toast element
     */
    success(message, title = '', options = {}) {
      return this.createToast('success', message, title, options);
    }
  
    /**
     * Show an error toast
     * 
     * @param {string} message - The message to display
     * @param {string} title - Optional title
     * @param {object} options - Optional override options
     * @returns {HTMLElement} The toast element
     */
    error(message, title = '', options = {}) {
      return this.createToast('error', message, title, options);
    }
  
    /**
     * Show an info toast
     * 
     * @param {string} message - The message to display
     * @param {string} title - Optional title
     * @param {object} options - Optional override options
     * @returns {HTMLElement} The toast element
     */
    info(message, title = '', options = {}) {
      return this.createToast('info', message, title, options);
    }
  
    /**
     * Show a warning toast
     * 
     * @param {string} message - The message to display
     * @param {string} title - Optional title
     * @param {object} options - Optional override options
     * @returns {HTMLElement} The toast element
     */
    warning(message, title = '', options = {}) {
      return this.createToast('warning', message, title, options);
    }
  
    /**
     * Escape HTML to prevent XSS
     * 
     * @param {string} html - The string to escape
     * @returns {string} The escaped string
     */
    escapeHtml(html) {
      const div = document.createElement('div');
      div.textContent = html;
      return div.innerHTML;
    }
  }
  
  // Create global instance
  window.customToastr = new CustomToastr();
  
  // Compatibility with existing toastr calls
  window.toastr = {
    success: (message, title, options) => window.customToastr.success(message, title, options),
    error: (message, title, options) => window.customToastr.error(message, title, options),
    info: (message, title, options) => window.customToastr.info(message, title, options),
    warning: (message, title, options) => window.customToastr.warning(message, title, options),
    clear: () => window.customToastr.clear()
  };
  