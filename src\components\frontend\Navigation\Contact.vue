<template>
  <section class="contact-section py-6 bg-gradient-to-br from-blue-50 to-blue-100">
    <div class="container mx-auto px-4">
      <div class="max-w-4xl mx-auto">
        <!-- Header -->
        <div class="text-center mb-4">
          <h2
            class="text-xl md:text-2xl font-bold text-blue-600 mb-1 relative inline-block"
          >
            Contact Us
            <span
              class="absolute bottom-0 left-0 w-full h-0.5 bg-gradient-to-r from-blue-400 to-blue-200 -mb-1"
            ></span>
          </h2>
          <p class="text-gray-600 text-sm mt-1">We'd love to hear from you</p>
        </div>

        <div
          class="grid grid-cols-1 md:grid-cols-2 gap-4 bg-white rounded-lg shadow-md overflow-hidden"
        >
          <!-- Contact Form -->
          <div class="p-4 md:p-5">
            <h3 class="text-lg font-semibold text-gray-800 mb-3">
              Feedback and Complaint Form
            </h3>

            <form @submit.prevent="submitForm" class="space-y-3">
              <!-- Name -->
              <div>
                <input
                  type="text"
                  v-model="form.complainersName"
                  placeholder="Complainer Name"
                  class="w-full px-3 py-2 text-sm rounded-md border border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-200 transition-all"
                  required
                />
              </div>

              <!-- Address -->
              <div>
                <input
                  type="address"
                  v-model="form.complainersAddress"
                  placeholder="Address"
                  class="w-full px-3 py-2 text-sm rounded-md border border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-200 transition-all"
                  required
                />
              </div>

              <!-- Phone -->
              <div>
                <input
                  type="tel"
                  v-model="form.complainersNumber"
                  placeholder="Phone"
                  class="w-full px-3 py-2 text-sm rounded-md border border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-200 transition-all"
                />
              </div>

              <!-- Complaint Type -->
              <div>
                <select
                  v-model="form.complainTypeId"
                  class="w-full px-3 py-2 text-sm rounded-md border border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-200 transition-all"
                  required
                >
                  <option value="" disabled selected>Select Complaint Type</option>
                  <option v-for="type in complaintTypes" :key="type" :value="type.value">
                    {{ type.label }}
                  </option>
                </select>
              </div>

              <!-- Message -->
              <div>
                <textarea
                  v-model="form.complainDescription"
                  placeholder="Complaint Message"
                  rows="3"
                  class="w-full px-3 py-2 text-sm rounded-md border border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-200 transition-all resize-none"
                  required
                ></textarea>
              </div>

              <!-- Captcha -->
              <div>
                <div class="flex items-center space-x-2">
                  <div class="text-gray-600 text-sm">
                    {{ captcha.num1 }} + {{ captcha.num2 }} = ?
                  </div>
                  <input
                    type="number"
                    v-model="form.captchaAnswer"
                    class="w-20 px-3 py-2 text-sm rounded-md border border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-200 transition-all"
                    required
                  />
                </div>
              </div>

              <!-- Submit Button -->
              <div>
                <button
                  type="submit"
                  class="bg-green-500 hover:bg-green-600 text-white text-sm font-medium py-2 px-4 rounded-md transition-colors duration-300"
                  :disabled="isSubmitting"
                >
                  <span v-if="isSubmitting">
                    <svg
                      class="animate-spin -ml-1 mr-2 h-3 w-3 text-white inline-block"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        class="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        stroke-width="4"
                      ></circle>
                      <path
                        class="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    Sending...
                  </span>
                  <span v-else>Send Message</span>
                </button>
              </div>
            </form>
          </div>

          <!-- Contact Information -->
          <div
            class="bg-gradient-to-br from-blue-600 to-blue-800 text-white p-4 md:p-5 relative overflow-hidden"
          >
            <!-- Decorative elements -->
            <div
              class="absolute top-0 right-0 w-32 h-32 bg-blue-400 rounded-full opacity-10 -mr-10 -mt-10"
            ></div>
            <div
              class="absolute bottom-0 left-0 w-24 h-24 bg-blue-400 rounded-full opacity-10 -ml-10 -mb-10"
            ></div>

            <div class="relative z-10">
              <h3
                class="text-lg font-semibold mb-4 border-b border-blue-400 pb-2 inline-block"
              >
                Contact Us
              </h3>

              <div class="space-y-3 text-sm">
                <div class="flex items-start">
                  <div class="text-blue-200 mr-3 mt-1">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-4 w-4"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                      />
                    </svg>
                  </div>
                  <div>
                    <p class="font-medium text-white">
                      Dharan Water Supply Management Board
                    </p>
                    <p class="text-blue-100">Dharan-10, Sunsari</p>
                  </div>
                </div>

                <div class="flex items-start">
                  <div class="text-blue-200 mr-3 mt-1">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-4 w-4"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                      />
                    </svg>
                  </div>
                  <div>
                    <p class="font-medium text-white">Telephone No:</p>
                    <p class="text-blue-100">056-493916, 056-493376</p>
                  </div>
                </div>

                <div class="flex items-start">
                  <div class="text-blue-200 mr-3 mt-1">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-4 w-4"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                      />
                    </svg>
                  </div>
                  <div>
                    <p class="font-medium text-white">Email:</p>
                    <p class="text-blue-100"><EMAIL></p>
                  </div>
                </div>
              </div>

              <!-- Map with styled container -->
              <div class="mt-4">
                <div
                  class="rounded-md overflow-hidden shadow-inner border border-blue-400 h-32"
                >
                  <iframe
                    src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2335.102004313564!2d87.28474297901103!3d26.827498117618834!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x39ef41725bd0fbeb%3A0x352c95e446046cfa!2sDharan%20Water%20Supply%20Management%20Board!5e1!3m2!1sen!2snp!4v1747885883750!5m2!1sen!2snp"
                    width="600"
                    height="450"
                    style="border: 0"
                    allowfullscreen=""
                    loading="lazy"
                    referrerpolicy="no-referrer-when-downgrade"
                  ></iframe>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import { onMounted, reactive, ref } from "vue";

import api from "@/api/api";
import { ORG_ID } from "@/api/endpoint";
import { useContactTypeStore } from "@/stores/contactStore";

const contactStore = useContactTypeStore();
const complaintTypes = contactStore.types;

// Form data
const form = reactive({
  orgId: ORG_ID,
  complainersName: "",
  complainersAddress: "",
  complainersNumber: "",
  complainTypeId: "",
  complainDescription: "",
  captchaAnswer: null,
});

// Captcha
const captcha = reactive({
  num1: 0,
  num2: 0,
});

// Form submission state
const isSubmitting = ref(false);
const formSubmitted = ref(false);

// Generate random captcha numbers
const generateCaptcha = () => {
  captcha.num1 = Math.floor(Math.random() * 10);
  captcha.num2 = Math.floor(Math.random() * 10);
};

// Submit form
const submitForm = async () => {
  // Validate captcha
  if (Number(form.captchaAnswer) !== captcha.num1 + captcha.num2) {
    alert("Incorrect captcha answer. Please try again.");
    generateCaptcha();
    form.captchaAnswer = null;
    return;
  }

  isSubmitting.value = true;

  try {
    // Simulate API call
    const response = await api.post(`/customer/saveComplainEntry`, form);
    if (response.status == 200) {
      alert("Complaint submitted successfully");
    } else {
      alert("Error submitting complaint");
    }

    console.log(form);
    // Reset form after successful submission
    Object.keys(form).forEach((key) => {
      if (typeof form[key] === "string") form[key] = "";
      else form[key] = null;
    });

    formSubmitted.value = true;
    // Generate new captcha
    generateCaptcha();
  } catch (error) {
    console.error("Error submitting form:", error);
    alert("There was an error sending your message. Please try again.");
  } finally {
    isSubmitting.value = false;
  }
};

// Initialize captcha on component mount
onMounted(() => {
  generateCaptcha();
});
</script>
