/* Custom Toastr Notifications
-------------------------------------------------- */
:root {
    --toastr-success-bg: #00c292;
    --toastr-info-bg: #03a9f3;
    --toastr-warning-bg: #fec107;
    --toastr-error-bg: #e46a76;
    --toastr-text-color: #ffffff;
    --toastr-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
    --toastr-border-radius: 8px;
    --toastr-font: 'Jost', sans-serif;
  }
  
  /* Toast Container */
  .custom-toast-container {
    position: fixed;
    z-index: 999999;
    pointer-events: none;
    padding: 15px;
    max-width: 100%;
    box-sizing: border-box;
  }
  
  .custom-toast-container.top-right {
    top: 15px;
    right: 15px;
  }
  
  .custom-toast-container.top-left {
    top: 15px;
    left: 15px;
  }
  
  .custom-toast-container.top-center {
    top: 15px;
    left: 50%;
    transform: translateX(-50%);
  }
  
  .custom-toast-container.bottom-right {
    bottom: 15px;
    right: 15px;
  }
  
  .custom-toast-container.bottom-left {
    bottom: 15px;
    left: 15px;
  }
  
  .custom-toast-container.bottom-center {
    bottom: 15px;
    left: 50%;
    transform: translateX(-50%);
  }
  
  /* Toast */
  .custom-toast {
    position: relative;
    pointer-events: auto;
    overflow: hidden;
    margin: 0 0 10px;
    padding: 15px 15px 15px 60px;
    width: 300px;
    max-width: 100%;
    border-radius: var(--toastr-border-radius);
    background-position: 15px center;
    background-repeat: no-repeat;
    background-size: 24px;
    box-shadow: var(--toastr-shadow);
    color: var(--toastr-text-color);
    opacity: 0;
    transform: translateY(-20px);
    transition: all 0.3s ease-in-out;
    font-family: var(--toastr-font);
  }
  
  .custom-toast.show {
    opacity: 1;
    transform: translateY(0);
  }
  
  .custom-toast.hide {
    opacity: 0;
    transform: translateY(-20px);
    transition: all 0.3s ease-in-out;
  }
  
  /* Toast Types */
  .custom-toast.success {
    background-color: var(--toastr-success-bg);
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M22 11.08V12a10 10 0 1 1-5.93-9.14'%3E%3C/path%3E%3Cpolyline points='22 4 12 14.01 9 11.01'%3E%3C/polyline%3E%3C/svg%3E");
  }
  
  .custom-toast.info {
    background-color: var(--toastr-info-bg);
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='12' y1='16' x2='12' y2='12'%3E%3C/line%3E%3Cline x1='12' y1='8' x2='12.01' y2='8'%3E%3C/line%3E%3C/svg%3E");
  }
  
  .custom-toast.warning {
    background-color: var(--toastr-warning-bg);
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z'%3E%3C/path%3E%3Cline x1='12' y1='9' x2='12' y2='13'%3E%3C/line%3E%3Cline x1='12' y1='17' x2='12.01' y2='17'%3E%3C/line%3E%3C/svg%3E");
  }
  
  .custom-toast.error {
    background-color: var(--toastr-error-bg);
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='15' y1='9' x2='9' y2='15'%3E%3C/line%3E%3Cline x1='9' y1='9' x2='15' y2='15'%3E%3C/line%3E%3C/svg%3E");
  }
  
  /* Toast Content */
  .custom-toast-title {
    font-weight: 600;
    font-size: 16px;
    margin-bottom: 5px;
  }
  
  .custom-toast-message {
    font-size: 14px;
    word-wrap: break-word;
  }
  
  /* Close Button */
  .custom-toast-close {
    position: absolute;
    right: 10px;
    top: 10px;
    width: 16px;
    height: 16px;
    opacity: 0.7;
    cursor: pointer;
    transition: opacity 0.3s ease;
    background: transparent;
    border: none;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .custom-toast-close:hover {
    opacity: 1;
  }
  
  .custom-toast-close:before,
  .custom-toast-close:after {
    position: absolute;
    content: '';
    height: 16px;
    width: 2px;
    background-color: var(--toastr-text-color);
  }
  
  .custom-toast-close:before {
    transform: rotate(45deg);
  }
  
  .custom-toast-close:after {
    transform: rotate(-45deg);
  }
  
  /* Progress Bar */
  .custom-toast-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    width: 100%;
    background-color: rgba(255, 255, 255, 0.3);
  }
  
  .custom-toast-progress-bar {
    height: 100%;
    width: 100%;
    background-color: rgba(255, 255, 255, 0.6);
    transition: width linear;
  }
  
  /* Dark Mode Support */
  .layout-dark .custom-toast {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  }
  
  /* Responsive */
  @media (max-width: 480px) {
    .custom-toast-container {
      left: 0;
      right: 0;
      width: 100%;
      transform: none;
    }
    
    .custom-toast {
      width: 100%;
      border-radius: 0;
      margin-bottom: 5px;
    }
    
    .custom-toast-container.top-center,
    .custom-toast-container.bottom-center {
      transform: none;
      left: 0;
    }
  }
  