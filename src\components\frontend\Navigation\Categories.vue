<template>
  <section class="categories-section py-8 bg-gradient-to-br from-blue-50 to-blue-100">
    <div class="container mx-auto px-4">
      <div class="max-w-6xl mx-auto">
        <!-- Header -->
        <div class="text-center mb-8">
          <h2
            class="text-2xl md:text-3xl font-bold text-blue-600 mb-2 relative inline-block"
          >
            Document Categories
            <span
              class="absolute bottom-0 left-0 w-full h-0.5 bg-gradient-to-r from-blue-400 to-blue-200 -mb-1"
            ></span>
          </h2>
          <p class="text-gray-600 mt-2">Access and download our official documents</p>
        </div>

        <!-- Categories Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <!-- Annual Report -->
          <router-link to="/publications/annual-reports" class="category-card">
            <div class="card-content">
              <div class="icon-container">
          <i class="fas fa-file-alt text-blue-500"></i>
              </div>
              <h3 class="card-title">Annual Report</h3>
            </div>
          </router-link>

          <!-- Audit Reports -->
          <router-link to="/publications/audit-reports" class="category-card">
            <div class="card-content">
              <div class="icon-container">
          <i class="fas fa-chart-line text-blue-500"></i>
              </div>
              <h3 class="card-title">Audit Reports</h3>
            </div>
          </router-link>

          <!-- Vacancy -->
          <router-link to="/career" class="category-card">
            <div class="card-content">
              <div class="icon-container">
          <i class="fas fa-user-tie text-blue-500"></i>
              </div>
              <h3 class="card-title">Vacancy</h3>
            </div>
          </router-link>

          <!-- Tenders -->
          <router-link to="/notices/tender-notices" class="category-card">
            <div class="card-content">
              <div class="icon-container">
          <i class="fas fa-gavel text-blue-500"></i>
              </div>
              <h3 class="card-title">Tenders</h3>
            </div>
          </router-link>

          <!-- Downloads -->
          <router-link to="/publications/downloads" class="category-card">
            <div class="card-content">
              <div class="icon-container">
          <i class="fas fa-download text-blue-500"></i>
              </div>
              <h3 class="card-title">Downloads</h3>
            </div>
          </router-link>

          <!-- Water Quality Test Reports -->
          <router-link to="/publications/water-quality-test-report" class="category-card">
            <div class="card-content">
              <div class="icon-container">
          <i class="fas fa-tint text-blue-500"></i>
              </div>
              <h3 class="card-title">Water Quality Test Reports</h3>
            </div>
          </router-link>

          <!-- Tariff & Others -->
          <router-link to="/notices/tariff-and-others" class="category-card">
            <div class="card-content">
              <div class="icon-container">
          <i class="fas fa-money-bill-wave text-blue-500"></i>
              </div>
              <h3 class="card-title">Tariff & Others</h3>
            </div>
          </router-link>

          <!-- Consumers Notice -->
          <router-link to="/notices/consumer-notice" class="category-card">
            <div class="card-content">
              <div class="icon-container">
          <i class="fas fa-bullhorn text-blue-500"></i>
              </div>
              <h3 class="card-title">Consumers Notice</h3>
            </div>
          </router-link>

          <!-- Careers -->
          <router-link to="/career" class="category-card">
            <div class="card-content">
              <div class="icon-container">
          <i class="fas fa-briefcase text-blue-500"></i>
              </div>
              <h3 class="card-title">Careers</h3>
            </div>
          </router-link>
        </div>

      </div>
    </div>
  </section>
</template>

<script setup>
import router from '@/router'

// No additional script needed for this component
</script>

<style scoped>
/* Using standard CSS instead of Tailwind's @apply */
.categories-section .category-card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  transition: all 0.3s;
  display: flex;
  flex-direction: column;
  position: relative;
}

.categories-section .category-card:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transform: translateY(-4px);
}

.categories-section .card-content {
  padding: 1.25rem;
  flex-grow: 1;
}

.categories-section .icon-container {
  margin-bottom: 0.75rem;
  font-size: 1.5rem;
}

.categories-section .card-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.categories-section .years-list {
  margin-top: 0.75rem;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.categories-section .year-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.875rem;
  color: #4b5563;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  transition: all 0.2s;
  position: relative;
  z-index: 3; /* Higher z-index to ensure clickability */
  text-decoration: none;
  cursor: pointer;
}

.categories-section .year-item:hover {
  background-color: #f3f4f6;
  color: #2563eb;
  transform: translateX(3px);
}

.categories-section .card-footer {
  background-color: #f9fafb;
  padding: 0.75rem;
  border-top: 1px solid #f3f4f6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.categories-section .category-info {
  font-size: 0.875rem;
  color: #6b7280;
}

.categories-section .card-link {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

/* Screen reader only class */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.categories-section .view-all-link {
  color: #2563eb;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  position: relative;
  z-index: 2;
}

/* Remove the full card link for the Water Quality card */
.categories-section .category-card:has(.years-list) .card-link {
  display: none;
}

/* Fallback for browsers that don't support :has */
.categories-section .years-list ~ .card-footer .card-link {
  display: none;
}
</style>
