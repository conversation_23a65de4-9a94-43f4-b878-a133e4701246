import { defineStore } from 'pinia'

// import api from '@/api/api' // Uncomment when API is ready

const mockData = [
    {
        "label": "Meter Reading Not done",
        "value": 7095
    },
    {
        "label": "Irregular Meter Reading",
        "value": 8426
    },
    {
        "label": "Irregular Water Supply",
        "value": 8427
    },
    {
        "label": "The water fell a little",
        "value": 8428
    },
    {
        "label": "Meter Broken",
        "value": 9441
    },
    {
        "label": "Meter Spoiled",
        "value": 9442
    },
    {
        "label": "Gate Valve  Spoiled",
        "value": 9443
    },
    {
        "label": "Other Check",
        "value": 9444
    },
    {
        "label": "To repair meter",
        "value": 9445
    },
    {
        "label": "Other",
        "value": 9446
    },
    {
        "label": "Seal broken",
        "value": 24929
    }
]

export const useContactTypeStore = defineStore('contactType', {
    state: () => ({
        types: [...mockData],
        loading: false,
        error: null
    }),
    getters: {
        allTypes: (state) => state.types
    },
    actions: {
        // Call this when API is ready
        // async fetchTypes() {
        //   this.loading = true
        //   this.error = null
        //   try {
        //     const response = await api.get('/types-endpoint')
        //     this.types = response.data // Adjust as per API response
        //   } catch (err) {
        //     this.error = err
        //   } finally {
        //     this.loading = false
        //   }
        // }
    }
})