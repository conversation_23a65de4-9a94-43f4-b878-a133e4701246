<script setup lang="ts">
import {
  onMounted,
  ref,
} from 'vue'

import api from '@/api/api'
import { ORG_ID } from '@/api/endpoint'
import RightExecutiveDirector from '@/components/RightExecutiveDirector.vue'

import FormComponent1 from '../FormComponent1.vue'

const aboutUsDetails = ref(null);

//API for The mockExecutive
async function fetchMockExecutive() {
  try {
    const functionalData = await api.get(
      `/customer/webComponent/${ORG_ID}/miscellaneous`
    );
    aboutUsDetails.value = functionalData.data.aboutUsDetails;
  } catch (error) {
    console.error("Error fetching about us data:", error);
  }
}

onMounted(() => {
  try {
    fetchMockExecutive();
  } catch (error) {}
});
//Lateron Api Call
</script>

<template>
  <section class="miscellaneous-section py-10 bg-gradient-to-br from-blue-50 to-blue-100">
    <div class="container mx-auto px-4">
      <div class="max-w-6xl mx-auto">
        <!-- Header -->
        <div class="text-center mb-8">
          <h2
            class="text-2xl md:text-3xl font-bold text-blue-600 mb-2 relative inline-block"
          >
            Miscellaneous
            <span
              class="absolute bottom-0 left-0 w-full h-0.5 bg-gradient-to-r from-blue-400 to-blue-200 -mb-1"
            ></span>
          </h2>
        </div>

        <!-- Main Content -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <!-- Left Column - Miscellaneous Information -->
          <div class="lg:col-span-2">
            <div
              class="bg-white rounded-xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl"
            >
              <div class="p-6">
                <!-- Government Power section -->
                <div class="mb-6" v-for="item in aboutUsDetails" :key="item.order">
                  <h3 class="text-xl font-bold text-blue-900 mb-3">
                    {{ item.header }}
                  </h3>
                  <div class="rich-text" v-html="item.message"></div>
                </div>
              </div>
            </div>
          </div>

          <!-- Right Column - Executive Director Profiles -->
          <RightExecutiveDirector />
        </div>
      </div>
    </div>
  </section>
  <FormComponent1 />
</template>
<style scoped>
/* Rich text content styles */
::v-deep(.rich-text ul) {
  list-style-type: disc;
  padding-left: 1.5rem;
  margin-bottom: 1rem;
}

::v-deep(.rich-text ol) {
  list-style-type: decimal;
  padding-left: 1.5rem;
  margin-bottom: 1rem;
}

::v-deep(.rich-text li) {
  margin-bottom: 0.25rem;
  line-height: 1.6;
}

::v-deep(.rich-text h1) {
  font-size: 2rem;
  font-weight: 700;
  margin-top: 1.5rem;
  margin-bottom: 1rem;
}

::v-deep(.rich-text h2) {
  font-size: 1.5rem;
  font-weight: 700;
  margin-top: 1.25rem;
  margin-bottom: 0.75rem;
}

::v-deep(.rich-text h3) {
  font-size: 1.25rem;
  font-weight: 600;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
}

::v-deep(.rich-text h4) {
  font-size: 1.125rem;
  font-weight: 600;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
}

::v-deep(.rich-text h5) {
  font-size: 1rem;
  font-weight: 600;
  margin-top: 0.75rem;
  margin-bottom: 0.5rem;
}

::v-deep(.rich-text h6) {
  font-size: 0.875rem;
  font-weight: 600;
  margin-top: 0.5rem;
  margin-bottom: 0.25rem;
}

::v-deep(.rich-text p) {
  margin-bottom: 0.5rem;
  line-height: 1.7;
}

::v-deep(.rich-text a) {
  color: #2563eb;
  text-decoration: underline;
  transition: color 0.2s;
}

::v-deep(.rich-text a:hover) {
  color: #1d4ed8;
}

::v-deep(.rich-text blockquote) {
  border-left: 4px solid #2563eb;
  background: #f1f5f9;
  padding: 0.75rem 1rem;
  margin: 1rem 0;
  font-style: italic;
  color: #334155;
}

::v-deep(.rich-text img) {
  max-width: 100%;
  height: auto;
  margin: 1rem 0;
  border-radius: 0.5rem;
}

::v-deep(.rich-text strong) {
  font-weight: 700;
}

::v-deep(.rich-text em) {
  font-style: italic;
}

::v-deep(.rich-text code) {
  background: #f3f4f6;
  color: #2563eb;
  padding: 0.2em 0.4em;
  border-radius: 0.25em;
  font-size: 0.95em;
  font-family: 'Fira Mono', 'Consolas', monospace;
}

::v-deep(.rich-text pre) {
  background: #f3f4f6;
  color: #334155;
  padding: 1em;
  border-radius: 0.5em;
  overflow-x: auto;
  margin: 1em 0;
  font-family: 'Fira Mono', 'Consolas', monospace;
  font-size: 0.95em;
}

::v-deep(.rich-text table) {
  width: 100%;
  border-collapse: collapse;
  margin: 1em 0;
}

::v-deep(.rich-text th),
::v-deep(.rich-text td) {
  border: 1px solid #e5e7eb;
  padding: 0.5em 1em;
  text-align: left;
}

::v-deep(.rich-text th) {
  background: #f1f5f9;
  font-weight: 600;
}

::v-deep(.rich-text hr) {
  border: none;
  border-top: 1px solid #e5e7eb;
  margin: 1.5em 0;
}

::v-deep(.rich-text ul ul),
::v-deep(.rich-text ol ol),
::v-deep(.rich-text ul ol),
::v-deep(.rich-text ol ul) {
  margin-bottom: 0;
  margin-top: 0.25rem;
}
</style>
