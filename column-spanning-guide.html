<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DataTable Column Spanning Guide</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #2563eb;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 10px;
        }
        h2 {
            color: #1e40af;
            margin-top: 30px;
            border-left: 4px solid #3b82f6;
            padding-left: 10px;
        }
        h3 {
            color: #1e3a8a;
            margin-top: 25px;
        }
        pre {
            background-color: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 15px;
            overflow-x: auto;
            font-family: 'Courier New', Courier, monospace;
        }
        code {
            font-family: 'Courier New', Courier, monospace;
            background-color: #f1f5f9;
            padding: 2px 4px;
            border-radius: 4px;
        }
        .note {
            background-color: #eff6ff;
            border-left: 4px solid #3b82f6;
            padding: 10px 15px;
            margin: 20px 0;
            border-radius: 0 6px 6px 0;
        }
        .warning {
            background-color: #fff7ed;
            border-left: 4px solid #f97316;
            padding: 10px 15px;
            margin: 20px 0;
            border-radius: 0 6px 6px 0;
        }
        .example {
            margin: 30px 0;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            overflow: hidden;
        }
        .example-header {
            background-color: #f8fafc;
            padding: 10px 15px;
            font-weight: 600;
            border-bottom: 1px solid #e2e8f0;
        }
        .example-content {
            padding: 15px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        table, th, td {
            border: 1px solid #e2e8f0;
        }
        th, td {
            padding: 10px;
            text-align: left;
        }
        th {
            background-color: #f8fafc;
        }
        img {
            max-width: 100%;
            height: auto;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
        }
        .step {
            background-color: #f8fafc;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            border-left: 4px solid #3b82f6;
        }
        .step-number {
            display: inline-block;
            background-color: #3b82f6;
            color: white;
            width: 24px;
            height: 24px;
            text-align: center;
            border-radius: 50%;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <h1>Complete Guide to Column Spanning in DataTable Component</h1>
    
    <p>This guide provides comprehensive instructions on how to use column spanning features in our DataTable component. Column spanning allows you to create more organized and hierarchical table structures by grouping related columns under common headers.</p>

    <div class="note">
        <strong>Note:</strong> Column spanning is available in version 1.2.0 and above of the DataTable component.
    </div>

    <h2>Table of Contents</h2>
    <ul>
        <li><a href="#basic-concepts">Basic Concepts</a></li>
        <li><a href="#step-by-step">Step-by-Step Implementation</a></li>
        <li><a href="#examples">Examples</a>
            <ul>
                <li><a href="#basic-example">Basic Column Grouping</a></li>
                <li><a href="#complex-example">Complex Multi-Level Headers</a></li>
                <li><a href="#mixed-example">Mixed Regular and Grouped Columns</a></li>
                <li><a href="#with-filters">Column Groups with Filters</a></li>
            </ul>
        </li>
        <li><a href="#api-reference">API Reference</a></li>
        <li><a href="#troubleshooting">Troubleshooting</a></li>
    </ul>

    <h2 id="basic-concepts">Basic Concepts</h2>
    
    <p>Column spanning in the DataTable component allows you to:</p>
    
    <ul>
        <li>Group related columns under a common header</li>
        <li>Create multi-level header structures</li>
        <li>Span columns horizontally (colspan) and vertically (rowspan)</li>
        <li>Mix regular columns with grouped columns</li>
    </ul>

    <h3>Key Properties</h3>
    
    <table>
        <tr>
            <th>Property</th>
            <th>Description</th>
            <th>Type</th>
            <th>Default</th>
        </tr>
        <tr>
            <td><code>children</code></td>
            <td>Array of child columns that will appear under this group</td>
            <td>Array</td>
            <td>-</td>
        </tr>
        <tr>
            <td><code>colspan</code></td>
            <td>Number of columns this header should span horizontally</td>
            <td>Number</td>
            <td>1</td>
        </tr>
        <tr>
            <td><code>rowspan</code></td>
            <td>Number of rows this header should span vertically</td>
            <td>Number</td>
            <td>1</td>
        </tr>
    </table>

    <h2 id="step-by-step">Step-by-Step Implementation</h2>

    <div class="step">
        <p><span class="step-number">1</span> <strong>Import the DataTable component</strong></p>
        <pre><code>import DataTable from '@/plugins/vue-data-table/components/DataTable.vue';</code></pre>
    </div>

    <div class="step">
        <p><span class="step-number">2</span> <strong>Define your column structure with grouping</strong></p>
        <pre><code>const columns = [
  // Regular column with rowspan
  { key: 'id', label: 'ID', rowspan: 2 },
  
  // Column group with children
  { 
    label: 'Student Information',
    colspan: 3,
    children: [
      { key: 'name', label: 'Name', sortable: true },
      { key: 'gender', label: 'Gender' },
      { key: 'dob', label: 'Date of Birth' }
    ]
  },
  
  // Another column group
  {
    label: 'Contact Information',
    colspan: 2,
    children: [
      { key: 'email', label: 'Email' },
      { key: 'phone', label: 'Phone' }
    ]
  },
  
  // Regular column with rowspan
  { key: 'status', label: 'Status', rowspan: 2 }
];</code></pre>
    </div>

    <div class="step">
        <p><span class="step-number">3</span> <strong>Use the DataTable component with your columns</strong></p>
        <pre><code>&lt;data-table
  :items="items"
  :columns="columns"
  title="Students"
  :loading="loading"
  :server-side="false"
  theme="primary"
&gt;
  &lt;!-- Optional custom cell templates --&gt;
  &lt;template #cell-gender="{ value }"&gt;
    &lt;span :class="['gender-badge', `gender-${value.toLowerCase()}`]"&gt;
      {{ value }}
    &lt;/span&gt;
  &lt;/template&gt;
&lt;/data-table&gt;</code></pre>
    </div>

    <div class="note">
        <p><strong>Important:</strong> When using column groups, make sure to:</p>
        <ul>
            <li>Set <code>rowspan: 2</code> for regular columns that should span both header rows</li>
            <li>Set <code>colspan</code> equal to the number of child columns in each group</li>
            <li>Ensure each child column has a unique <code>key</code> property</li>
        </ul>
    </div>

    <h2 id="examples">Examples</h2>

    <h3 id="basic-example">Basic Column Grouping</h3>
    
    <div class="example">
        <div class="example-header">Basic Column Grouping Example</div>
        <div class="example-content">
            <pre><code>&lt;template&gt;
  &lt;div&gt;
    &lt;data-table
      :items="students"
      :columns="columns"
      title="Students List"
      :loading="loading"
      theme="primary"
    &gt;&lt;/data-table&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref } from 'vue';
import DataTable from '@/plugins/vue-data-table/components/DataTable.vue';

const loading = ref(false);

// Column definitions with basic grouping
const columns = [
  { key: 'id', label: 'ID', rowspan: 2 },
  { 
    label: 'Personal Information',
    colspan: 3,
    children: [
      { key: 'name', label: 'Name' },
      { key: 'gender', label: 'Gender' },
      { key: 'age', label: 'Age' }
    ]
  },
  { 
    label: 'Academic Information',
    colspan: 2,
    children: [
      { key: 'grade', label: 'Grade' },
      { key: 'gpa', label: 'GPA' }
    ]
  }
];

// Sample data
const students = ref([
  { id: 1, name: 'John Doe', gender: 'Male', age: 18, grade: '12th', gpa: 3.8 },
  { id: 2, name: 'Jane Smith', gender: 'Female', age: 17, grade: '11th', gpa: 4.0 },
  { id: 3, name: 'Bob Johnson', gender: 'Male', age: 18, grade: '12th', gpa: 3.5 }
]);
&lt;/script&gt;</code></pre>
        </div>
    </div>

    <h3 id="complex-example">Complex Multi-Level Headers</h3>
    
    <div class="example">
        <div class="example-header">Complex Multi-Level Headers Example</div>
        <div class="example-content">
            <pre><code>&lt;template&gt;
  &lt;div&gt;
    &lt;data-table
      :items="busRoutes"
      :columns="columns"
      title="Bus Routes Performance"
      :loading="loading"
      theme="primary"
    &gt;
      &lt;!-- Custom cell rendering for ratios --&gt;
      &lt;template #cell-ticket_increment_ratio="{ value }"&gt;
        &lt;span :class="['ratio-badge', getRatioClass(value)]"&gt;
          {{ formatRatio(value) }}
        &lt;/span&gt;
      &lt;/template&gt;
    &lt;/data-table&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref } from 'vue';
import DataTable from '@/plugins/vue-data-table/components/DataTable.vue';

const loading = ref(false);

// Column definitions with complex grouping
const columns = [
  { key: 'sn', label: 'S.N', rowspan: 2 },
  { key: 'route', label: 'Route', rowspan: 2 },
  { key: 'bus_no', label: 'Bus No.', rowspan: 2 },
  { 
    label: 'Previous', 
    colspan: 4,
    children: [
      { key: 'prev_total_seats', label: 'Total Seats' },
      { key: 'prev_available', label: 'Available' },
      { key: 'prev_seat_count', label: 'Seat Count' },
      { key: 'prev_sales', label: 'Sales' }
    ]
  },
  { 
    label: 'Current', 
    colspan: 4,
    children: [
      { key: 'curr_total_seats', label: 'Total Seats' },
      { key: 'curr_available', label: 'Available' },
      { key: 'curr_seat_count', label: 'Seat Count' },
      { key: 'curr_sales', label: 'Sales' }
    ]
  },
  {
    label: 'Increment Ratio',
    colspan: 3,
    children: [
      { key: 'ticket_increment_ratio', label: 'Ticket Increment Ratio' },
      { key: 'sales_increment_ratio', label: 'Sales Increment Ratio' },
      { key: 'complaint_ratio', label: 'Complaint Ratio' }
    ]
  }
];

// Sample data
const busRoutes = ref([
  {
    sn: 1,
    route: 'Kathmandu - Pokhara',
    bus_no: 'BA-1-KHA-2345',
    prev_total_seats: 40,
    prev_available: 35,
    prev_seat_count: 32,
    prev_sales: 12800,
    curr_total_seats: 45,
    curr_available: 42,
    curr_seat_count: 40,
    curr_sales: 18000,
    ticket_increment_ratio: 0.25,
    sales_increment_ratio: 0.41,
    complaint_ratio: -0.15
  },
  // More data...
]);

// Helper functions
function formatRatio(value) {
  const percent = (value * 100).toFixed(1);
  return `${percent > 0 ? '+' : ''}${percent}%`;
}

function getRatioClass(value, isComplaint = false) {
  // For complaint ratio, negative is good
  if (isComplaint) {
    if (value < -0.1) return 'ratio-good';
    if (value > 0.1) return 'ratio-bad';
    return 'ratio-neutral';
  }
  
  // For other ratios, positive is good
  if (value > 0.1) return 'ratio-good';
  if (value < -0.1) return 'ratio-bad';
  return 'ratio-neutral';
}
&lt;/script&gt;

&lt;style&gt;
.ratio-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.ratio-good {
  background-color: #dcfce7;
  color: #166534;
}

.ratio-bad {
  background-color: #fee2e2;
  color: #991b1b;
}

.ratio-neutral {
  background-color: #f1f5f9;
  color: #475569;
}
&lt;/style&gt;</code></pre>
        </div>
    </div>

    <h3 id="mixed-example">Mixed Regular and Grouped Columns</h3>
    
    <div class="example">
        <div class="example-header">Mixed Regular and Grouped Columns Example</div>
        <div class="example-content">
            <pre><code>&lt;template&gt;
  &lt;div&gt;
    &lt;data-table
      :items="employees"
      :columns="columns"
      title="Employee Directory"
      :loading="loading"
      theme="primary"
    &gt;&lt;/data-table&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref } from 'vue';
import DataTable from '@/plugins/vue-data-table/components/DataTable.vue';

const loading = ref(false);

// Column definitions with mixed regular and grouped columns
const columns = [
  { key: 'id', label: 'ID', rowspan: 2 },
  { key: 'name', label: 'Name', rowspan: 2, sortable: true },
  { 
    label: 'Contact Information',
    colspan: 3,
    children: [
      { key: 'email', label: 'Email' },
      { key: 'phone', label: 'Phone' },
      { key: 'address', label: 'Address' }
    ]
  },
  { key: 'department', label: 'Department', rowspan: 2 },
  { key: 'position', label: 'Position', rowspan: 2 },
  { 
    label: 'Performance',
    colspan: 2,
    children: [
      { key: 'rating', label: 'Rating' },
      { key: 'attendance', label: 'Attendance %' }
    ]
  }
];

// Sample data
const employees = ref([
  {
    id: 1,
    name: 'John Doe',
    email: '<EMAIL>',
    phone: '************',
    address: '123 Main St',
    department: 'Engineering',
    position: 'Senior Developer',
    rating: 4.5,
    attendance: 98
  },
  // More data...
]);
&lt;/script&gt;</code></pre>
        </div>
    </div>

    <h3 id="with-filters">Column Groups with Filters</h3>
    
    <div class="example">
        <div class="example-header">Column Groups with Filters Example</div>
        <div class="example-content">
            <pre><code>&lt;template&gt;
  &lt;div&gt;
    &lt;data-table
      :items="admissions"
      :columns="columns"
      title="Student Admissions"
      :loading="loading"
      :server-side="true"
      :total-items="totalItems"
      :current-page-server="currentPage"
      :page-size="pageSize"
      :show-column-filters="true"
      :filterable-columns="['child_name', 'gender', 'child_dob', 'parent_name', 'relation']"
      :filter-config="filterConfig"
      @page-change="handlePageChange"
      @page-size-change="handlePageSizeChange"
      @sort="handleSort"
      @search="handleSearch"
      @column-filter="handleColumnFilter"
      theme="primary"
    &gt;
      &lt;!-- Custom cell templates --&gt;
      &lt;template #cell-gender="{ value }"&gt;
        &lt;span :class="['gender-badge', `gender-${value.toLowerCase()}`]"&gt;
          {{ value }}
        &lt;/span&gt;
      &lt;/template&gt;
      
      &lt;template #cell-child_dob="{ value }"&gt;
        {{ formatDate(value) }}
      &lt;/template&gt;
    &lt;/data-table&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref, onMounted } from 'vue';
import axios from 'axios';
import DataTable from '@/plugins/vue-data-table/components/DataTable.vue';

// Column definitions with grouping
const columns = [
  { key: 'id', label: 'ID', rowspan: 2 },
  { 
    label: 'Student Information',
    colspan: 3,
    children: [
      { key: 'child_name', label: 'Child Name', sortable: true },
      { key: 'gender', label: 'Gender' },
      { key: 'child_dob', label: 'Date of Birth' }
    ]
  },
  {
    label: 'Parent Information',
    colspan: 3,
    children: [
      { key: 'parent_name', label: 'Parent Name', sortable: true },
      { key: 'relation', label: 'Relation' },
      { key: 'phone_no', label: 'Phone Number' }
    ]
  },
  { key: 'email', label: 'Email', rowspan: 2 }
];

// Filter configuration
const filterConfig = {
  gender: {
    type: 'select',
    options: [
      { value: 'male', label: 'Male' },
      { value: 'female', label: 'Female' }
    ]
  },
  relation: {
    type: 'select',
    options: [
      { value: 'Father', label: 'Father' },
      { value: 'Mother', label: 'Mother' },
      { value: 'Guardian', label: 'Guardian' }
    ]
  },
  child_dob: {
    type: 'daterange'
  },
  child_name: {
    type: 'text'
  },
  parent_name: {
    type: 'text'
  }
};

// State variables
const admissions = ref([]);
const loading = ref(false);
const currentPage = ref(1);
const pageSize = ref(10);
const totalItems = ref(0);
const sortKey = ref('');
const sortOrder = ref('asc');
const searchQuery = ref('');
const columnFilters = ref({});

// Fetch admissions from API
async function fetchAdmissions() {
  loading.value = true;

  try {
    // Prepare parameters for axios
    const params = {
      page: currentPage.value,
      limit: pageSize.value
    };

    // Add sorting parameters if available
    if (sortKey.value) {
      params.sortBy = sortKey.value;
      params.sortOrder = sortOrder.value;
    }

    // Add search parameter if available
    if (searchQuery.value) {
      params.search = searchQuery.value;
    }
    
    // Add column filters if available
    if (Object.keys(columnFilters.value).length > 0) {
      params.filters = JSON.stringify(columnFilters.value);
    }

    // Make the API call using axios
    const response = await axios.get('http://your-api.com/admissions', { params });
    const result = response.data;

    // Extract data from the response
    if (result.success && result.data) {
      admissions.value = result.data.data;
      totalItems.value = result.data.total;
      currentPage.value = result.data.current_page;
    }
  } catch (error) {
    console.error('Error fetching admissions:', error);
  } finally {
    loading.value = false;
  }
}

// Event handlers
function handlePageChange(page) {
  currentPage.value = page;
  fetchAdmissions();
}

function handlePageSizeChange(size) {
  pageSize.value = size;
  currentPage.value = 1;
  fetchAdmissions();
}

function handleSort({ key, order }) {
  sortKey.value = key;
  sortOrder.value = order;
  fetchAdmissions();
}

function handleSearch(query) {
  searchQuery.value = query;
  currentPage.value = 1;
  fetchAdmissions();
}

function handleColumnFilter(filters) {
  columnFilters.value = filters;
  currentPage.value = 1;
  fetchAdmissions();
}

// Helper function to format date
function formatDate(dateString) {
  if (!dateString) return '';
  
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
}

// Load data when component mounts
onMounted(() => {
  fetchAdmissions();
});
&lt;/script&gt;

&lt;style&gt;
.gender-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.gender-male {
  background-color: #dbeafe;
  color: #1e40af;
}

.gender-female {
  background-color: #fce7f3;
  color: #9d174d;
}
&lt;/style&gt;</code></pre>
        </div>
    </div>

    <h2 id="api-reference">API Reference</h2>

    <h3>Column Definition Properties</h3>
    
    <table>
        <tr>
            <th>Property</th>
            <th>Type</th>
            <th>Description</th>
            <th>Required</th>
        </tr>
        <tr>
            <td><code>key</code></td>
            <td>String</td>
            <td>Unique identifier for the column, used to access data from items</td>
            <td>Yes (for leaf columns)</td>
        </tr>
        <tr>
            <td><code>label</code></td>
            <td>String</td>
            <td>Display text for the column header</td>
            <td>Yes</td>
        </tr>
        <tr>
            <td><code>children</code></td>
            <td>Array</td>
            <td>Array of child column definitions for grouped columns</td>
            <td>Yes (for group columns)</td>
        </tr>
        <tr>
            <td><code>colspan</code></td>
            <td>Number</td>
            <td>Number of columns this header should span horizontally</td>
            <td>No (default: 1)</td>
        </tr>
        <tr>
            <td><code>rowspan</code></td>
            <td>Number</td>
            <td>Number of rows this header should span vertically</td>
            <td>No (default: 1)</td>
        </tr>
        <tr>
            <td><code>sortable</code></td>
            <td>Boolean</td>
            <td>Whether the column can be sorted</td>
            <td>No (default: false)</td>
        </tr>
    </table>

    <h3>Column Group Structure</h3>

    <pre><code>// Column group structure
{
  label: 'Group Header',  // Required: Display text for the group
  colspan: 3,             // Required: Number of child columns
  children: [             // Required: Array of child column definitions
    {
      key: 'child1',      // Required: Unique identifier for the column
      label: 'Child 1',   // Required: Display text for the column
      sortable: true      // Optional: Whether the column can be sorted
    },
    // More child columns...
  ]
}</code></pre>

    <h3>Regular Column with Rowspan</h3>

    <pre><code>// Regular column with rowspan
{
  key: 'id',        // Required: Unique identifier for the column
  label: 'ID',      // Required: Display text for the column
  rowspan: 2,       // Required when using column groups: Makes the column span both header rows
  sortable: true    // Optional: Whether the column can be sorted
}</code></pre>

    <h2 id="troubleshooting">Troubleshooting</h2>

    <h3>Common Issues and Solutions</h3>

    <div class="warning">
        <strong>Issue:</strong> Column headers are not aligned correctly.
        <p><strong>Solution:</strong> Make sure the <code>colspan</code> value for each group matches the exact number of child columns in that group.</p>
    </div>

    <div class="warning">
        <strong>Issue:</strong> Regular columns are not spanning both header rows.
        <p><strong>Solution:</strong> Add <code>rowspan: 2</code> to all regular columns when using column groups.</p>
    </div>

    <div class="warning">
        <strong>Issue:</strong> Error: "Cannot read properties of undefined (reading 'columns')".
        <p><strong>Solution:</strong> In the template, use <code>columns</code> directly instead of <code>props.columns</code>.</p>
    </div>

    <div class="warning">
        <strong>Issue:</strong> Column filters not working with grouped columns.
        <p><strong>Solution:</strong> Make sure to use the child column keys in the <code>filterable-columns</code> array, not the group labels.</p>
    </div>

    <h3>Best Practices</h3>

    <ol>
        <li>Always set <code>rowspan: 2</code> for regular columns when using column groups</li>
        <li>Ensure each child column has a unique <code>key</code> property</li>
        <li>Set <code>colspan</code> equal to the exact number of child columns</li>
        <li>For sortable columns, add the <code>sortable: true</code> property to the child columns, not the group</li>
        <li>When using filters with grouped columns, specify the child column keys in the <code>filterable-columns</code> array</li>
    </ol>

    <div class="note">
        <strong>Tip:</strong> For complex tables with many columns, consider using horizontal scrolling by adding CSS:
        <pre><code>.table-responsive {
  overflow-x: auto;
  max-width: 100%;
}</code></pre>
    </div>

    <h2>Conclusion</h2>

    <p>Column spanning is a powerful feature that allows you to create more organized and hierarchical table structures. By grouping related columns under common headers, you can improve the readability and usability of your tables, especially for complex data sets.</p>

    <p>This guide covered all the ways to use column spanning in our DataTable component, from basic grouping to complex multi-level headers with filters. By following the examples and best practices outlined here, you can create professional-looking tables that effectively present your data.</p>
</body>
</html>
