import { defineStore } from 'pinia'

import api from '@/api/api'
import { ORG_ID } from '@/api/endpoint'

export const useMemberStore = defineStore('member', {
    state: () => ({
        status: 0,
        committee: [],
    }),
    getters: {
        getCommittees: (state) => state.committee,
        getCommitteeById: (state) => (id) =>
            state.committee.find((c) => c.id === id),

        getCommitteeMemberImages: (state) => (committeeName = "", count = Infinity) => {
            const normalize = str => str?.toLowerCase().replace(/s$/, '').trim();
            const committee = state.committee.find(
                c => normalize(c.name) === normalize(committeeName)
            );
            if (!committee) return [];
            return committee.subHeaders
                .slice(0, count)
                .map(member => ({
                    image: member.photoUrl,
                    name: member.name,
                    staffDesignation: member.staffDesignation,
                    staffContactNumber1: member.staffContactNumber1
                }));
        },
    },
    actions: {
        async fetchCommittees() {
            this.status = 0;
            this.committee = [];

            try {
                const response = await api.get(`/customer/staffCommittee2/${ORG_ID}`);

                if (response.data && response.data.status === 1 && response.data.committee) {
                    this.status = response.data.status;
                    this.committee = response.data.committee;
                    console.log(this.committee);
                    return true;
                } else {
                    console.error('Invalid API response format:', response.data);
                }
            } catch (error) {
                console.error('Error fetching committee data:', error);
            }
        },
        async fetchcomitteMember() {
            this.status = 0;
            this.committee = [];

            try {
                const response = await api.get(`/customer/committeeMember/${ORG_ID}`);
                if (response.data) {
                    return response.data;
                }
                else {
                    console.error('Invalid API response format:', response.data);
                }
            } catch (error) {
                console.error('Error fetching committee data:', error);
            }
        },
        async fetchFullCommittees() {
            this.status = 0;
            this.committee = [];

            try {
                const response = await api.get(`/customer/staffCommittee2/${ORG_ID}`);

                if (response.data && response.data.status === 1 && response.data.committee) {
                    return response.data;
                } else {
                    console.error('Invalid API response format:', response.data);
                }
            } catch (error) {
                console.error('Error fetching committee data:', error);
            }
        },

    },
});
