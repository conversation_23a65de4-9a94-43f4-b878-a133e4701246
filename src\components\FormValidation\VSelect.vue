<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { validateValue } from '@/utils/validation'

const props = defineProps({
  modelValue: {
    type: [String, Number, Array],
    default: ''
  },
  label: {
    type: String,
    default: ''
  },
  name: {
    type: String,
    default: ''
  },
  options: {
    type: Array,
    default: () => []
  },
  placeholder: {
    type: String,
    default: 'Select an option'
  },
  validations: {
    type: Array,
    default: () => []
  },
  formValues: {
    type: Object,
    default: () => ({})
  },
  validateOnBlur: {
    type: Boolean,
    default: true
  },
  validateOnChange: {
    type: Boolean,
    default: true
  },
  validateOnMount: {
    type: Boolean,
    default: false
  },
  customClass: {
    type: String,
    default: ''
  },
  size: {
    type: String,
    default: 'medium', // small, medium, large
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  },
  disabled: {
    type: Boolean,
    default: false
  },
  multiple: {
    type: <PERSON><PERSON>an,
    default: false
  },
  valueKey: {
    type: String,
    default: 'value'
  },
  labelKey: {
    type: String,
    default: 'label'
  }
})

const emit = defineEmits(['update:modelValue', 'validation', 'blur', 'focus', 'change'])

const selectValue = computed({
  get: () => props.modelValue,
  set: (value) => {
    emit('update:modelValue', value)
    if (props.validateOnChange) {
      validate()
    }
  }
})

const errors = ref([])
const touched = ref(false)
const validating = ref(false)

const isValid = computed(() => errors.value.length === 0)
const showError = computed(() => touched.value && !isValid.value)

const sizeClass = computed(() => {
  switch (props.size) {
    case 'small': return 'ih-small'
    case 'large': return 'ih-large'
    default: return 'ih-medium'
  }
})

const selectClasses = computed(() => {
  return {
    'form-select': true,
    'is-invalid': showError.value,
    'is-valid': touched.value && isValid.value,
    [sizeClass.value]: true,
    [props.customClass]: !!props.customClass,
    'disabled': props.disabled
  }
})

const validate = async () => {
  validating.value = true
  const { errors: validationErrors } = await validateValue(
    selectValue.value,
    props.validations,
    props.formValues
  )
  errors.value = validationErrors
  emit('validation', { valid: isValid.value, errors: errors.value, field: props.name })
  validating.value = false
  return isValid.value
}

const handleBlur = (event) => {
  touched.value = true
  if (props.validateOnBlur) {
    validate()
  }
  emit('blur', event)
}

const handleFocus = (event) => {
  emit('focus', event)
}

const handleChange = (event) => {
  emit('change', event)
}

watch(() => props.formValues, () => {
  if (touched.value) {
    validate()
  }
}, { deep: true })

onMounted(() => {
  if (props.validateOnMount) {
    touched.value = true
    validate()
  }
})

defineExpose({ validate, errors, isValid })
</script>

<template>
  <div class="form-group">
    <label v-if="label" :for="name">{{ label }}</label>
    
    <select
      :id="name"
      :name="name"
      :class="selectClasses"
      v-model="selectValue"
      :disabled="disabled"
      :multiple="multiple"
      @blur="handleBlur"
      @focus="handleFocus"
      @change="handleChange"
    >
      <option v-if="!multiple" value="" disabled>{{ placeholder }}</option>
      <option
        v-for="option in options"
        :key="typeof option === 'object' ? option[valueKey] : option"
        :value="typeof option === 'object' ? option[valueKey] : option"
      >
        {{ typeof option === 'object' ? option[labelKey] : option }}
      </option>
    </select>
    
    <div v-if="showError" class="invalid-feedback">
      {{ errors[0] }}
    </div>
  </div>
</template>

<style scoped>
.form-group {
  margin-bottom: 1.5rem;
}
</style>
