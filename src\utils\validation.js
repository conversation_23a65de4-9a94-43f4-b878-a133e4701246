/**
 * Validation utility for Vue forms
 * Provides a set of common validation rules and a validation engine
 */

/**
 * Built-in validation rules
 */
export const rules = {
  required: (value) => !!value || value === 0 || 'This field is required',
  
  email: (value) => {
    if (!value) return true // Skip if empty (use required rule for required fields)
    const pattern = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
    return pattern.test(value) || 'Please enter a valid email address'
  },
  
  minLength: (min) => (value) => {
    if (!value) return true // Skip if empty
    return value.length >= min || `Must be at least ${min} characters`
  },
  
  maxLength: (max) => (value) => {
    if (!value) return true // Skip if empty
    return value.length <= max || `Must be less than ${max} characters`
  },
  
  numeric: (value) => {
    if (!value) return true // Skip if empty
    return !isNaN(parseFloat(value)) && isFinite(value) || 'Must be a number'
  },
  
  integer: (value) => {
    if (!value) return true // Skip if empty
    return Number.isInteger(Number(value)) || 'Must be an integer'
  },
  
  min: (min) => (value) => {
    if (!value && value !== 0) return true // Skip if empty
    return Number(value) >= min || `Must be at least ${min}`
  },
  
  max: (max) => (value) => {
    if (!value && value !== 0) return true // Skip if empty
    return Number(value) <= max || `Must be less than ${max}`
  },
  
  pattern: (pattern, message = 'Invalid format') => (value) => {
    if (!value) return true // Skip if empty
    return pattern.test(value) || message
  },
  
  match: (field, fieldName) => (value, formValues) => {
    if (!value) return true // Skip if empty
    return value === formValues[field] || `Must match ${fieldName || field}`
  }
}

/**
 * Validates a single value against an array of validation rules
 * @param {any} value - The value to validate
 * @param {Array} validations - Array of validation rules
 * @param {Object} formValues - All form values (for cross-field validation)
 * @returns {Promise<{valid: boolean, errors: Array}>} - Validation result
 */
export const validateValue = async (value, validations = [], formValues = {}) => {
  if (!validations || !validations.length) {
    return { valid: true, errors: [] }
  }

  const errors = []
  
  for (const validation of validations) {
    // Handle async validations
    const result = validation(value, formValues)
    const validationResult = result instanceof Promise ? await result : result
    
    if (validationResult !== true) {
      errors.push(validationResult)
    }
  }
  
  return {
    valid: errors.length === 0,
    errors
  }
}

/**
 * Validates an entire form
 * @param {Object} values - Form values
 * @param {Object} validationRules - Validation rules for each field
 * @returns {Promise<{valid: boolean, errors: Object}>} - Validation result
 */
export const validateForm = async (values, validationRules) => {
  const errors = {}
  let isValid = true
  
  for (const field in validationRules) {
    const { valid, errors: fieldErrors } = await validateValue(
      values[field], 
      validationRules[field],
      values
    )
    
    if (!valid) {
      errors[field] = fieldErrors
      isValid = false
    }
  }
  
  return {
    valid: isValid,
    errors
  }
}

export default {
  rules,
  validateValue,
  validateForm
}
