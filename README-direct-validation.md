# Direct HTML Validation

A lightweight, easy-to-use validation system that works directly with standard HTML elements.

## Features

- ✅ Works with standard HTML elements - no custom components required
- ✅ Simple, intuitive API
- ✅ Great UI feedback with animations
- ✅ Flexible validation rules
- ✅ Form-level and field-level validation
- ✅ Customizable error messages and styling

## How to Use

### 1. Create a Validator

```javascript
import { createValidator } from '@/plugins/simpleValidator'

// Create a validator for the form
const validator = createValidator('#my-form', {
  validateOnBlur: true,
  validateOnSubmit: true,
  showSuccessState: true
})
```

### 2. Add Validation Rules

```javascript
// Add validation rules
validator
  .required('#name', 'Please enter your name')
  .email('#email', 'Please enter a valid email')
  .minLength('#password', 8, 'Password must be at least 8 characters')
  .match('#confirm-password', '#password', 'Passwords must match')
```

### 3. Handle Form Submission

```javascript
// Create a validator with callbacks
const validator = createValidator('#my-form', {
  onSuccess: (data) => {
    console.log('Form submitted:', data)
    // Process form data
    return false // Prevent default form submission
  },
  onError: (errors) => {
    console.log('Validation errors:', errors)
  }
})
```

### 4. Reset the Form

```javascript
// Reset the form and validation state
document.querySelector('#reset-button').addEventListener('click', () => {
  document.querySelector('#my-form').reset()
  validator.reset()
})
```

## Available Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `validateOnBlur` | Boolean | `true` | Validate fields when they lose focus |
| `validateOnInput` | Boolean | `false` | Validate fields as the user types |
| `validateOnSubmit` | Boolean | `true` | Validate all fields on form submission |
| `showSuccessState` | Boolean | `true` | Show success state for valid fields |
| `scrollToError` | Boolean | `true` | Scroll to the first error field |
| `errorClass` | String | `'is-invalid'` | CSS class for invalid fields |
| `successClass` | String | `'is-valid'` | CSS class for valid fields |
| `errorMessageClass` | String | `'error-message'` | CSS class for error messages |
| `errorMessageTag` | String | `'div'` | HTML tag for error messages |
| `errorMessagePosition` | String | `'after'` | Where to insert error messages (`'after'`, `'before'`, `'append'`, `'prepend'`) |
| `onSuccess` | Function | `null` | Callback function when form is valid |
| `onError` | Function | `null` | Callback function when form has errors |

## Available Validation Rules

### Required

```javascript
validator.required('#field', 'This field is required')
```

### Email

```javascript
validator.email('#email', 'Please enter a valid email address')
```

### Min Length

```javascript
validator.minLength('#password', 8, 'Password must be at least 8 characters')
```

### Max Length

```javascript
validator.maxLength('#message', 500, 'Message must be less than 500 characters')
```

### Min Value

```javascript
validator.min('#age', 18, 'You must be at least 18 years old')
```

### Max Value

```javascript
validator.max('#quantity', 100, 'Maximum quantity is 100')
```

### Pattern

```javascript
validator.pattern('#zip', /^\d{5}$/, 'Please enter a valid ZIP code')
```

### Match

```javascript
validator.match('#confirm-password', '#password', 'Passwords must match')
```

### Custom

```javascript
validator.custom('#terms', value => value === true, 'You must accept the terms and conditions')
```

## Validator Methods

### addRule

Add a custom validation rule to a field.

```javascript
validator.addRule('#field', {
  test: value => value.includes('@'),
  message: 'Field must contain @'
})
```

### validate

Validate all fields and return true if all are valid.

```javascript
const isValid = validator.validate()
```

### reset

Reset the validation state.

```javascript
validator.reset()
```

### isValid

Check if the form is valid.

```javascript
if (validator.isValid()) {
  // Form is valid
}
```

### getErrors

Get all validation errors.

```javascript
const errors = validator.getErrors()
```

## HTML Structure

For best results, wrap your form elements in a container with the class `form-group`:

```html
<div class="form-group">
  <label for="name">Name</label>
  <input type="text" id="name" name="name">
  <!-- Error message will be inserted here -->
</div>
```

## Styling

The validation system comes with default styling for error and success states. You can customize the styling by modifying the CSS classes in your own stylesheet.

## Example

See the complete example at `/direct-validation` in your application.

## Why Use This Validation System?

1. **Simplicity**: Works directly with standard HTML elements - no custom components required
2. **Flexibility**: Validate any type of form element with a simple, intuitive API
3. **Performance**: Lightweight and optimized for performance
4. **Great UX**: Beautiful animations and visual feedback
5. **Customization**: Easy to customize with your own styling and error messages

---

Happy coding! 🚀
