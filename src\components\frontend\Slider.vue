<template>
  <section class="relative slider-section" v-if="images.length>0">
    <div class="slider-container">
      <div
        v-for="(image, index) in images"
        :key="index"
        :class="['slider-item', index === 0 ? 'active' : '']"
      >
        <img :src="image" alt="Slider Image" class="w-full h-full object-cover" />
      </div>
    </div>

    <!-- Slider Navigation Arrows -->
    <button class="slider-arrow slider-arrow-left" aria-label="Previous slide">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
        class="w-6 h-6"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M15 19l-7-7 7-7"
        />
      </svg>
    </button>
    <button class="slider-arrow slider-arrow-right" aria-label="Next slide">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
        class="w-6 h-6"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M9 5l7 7-7 7"
        />
      </svg>
    </button>

    <!-- Slider Navigation Dots -->
    <div class="absolute bottom-4 left-0 right-0 flex justify-center">
      <div class="slider-dots-container">
        <div
          v-for="(image, index) in images"
          :key="index"
          :class="['slider-dot', index === 0 ? 'active' : '']"
        ></div>
      </div>
    </div>
  </section>
  <div v-else class="no-image-found">
    No Image Found 
  </div>

  <!-- What's New Section -->
  <section class="bg-blue-900 text-white py-2">
    <div class="container mx-auto px-4 flex items-center">
      <div class="font-bold mr-4">WHAT'S NEW:</div>
      <div class="marquee overflow-hidden flex-1">
        <span>{{ whatsNew }}</span>
      </div>
    </div>
  </section>
</template>

<script setup>
import {
  onBeforeUnmount,
  onMounted,
  ref,
} from 'vue'

import api from '@/api/api'
import { ORG_ID } from '@/api/endpoint'

let sliderInterval;
let currentIndex = ref(0);

const whatsNew = ref(null);

const whatsNewData = {
  status: 1,
  aboutUsNewsAndImg: [
    {
      newsId: 437,
      header: "शुद्ध पानी पिउनुहोस् ।",
      order: 1,
      message:
        "<p>स्वच्छ सफा पानी पिउने&nbsp;बानी बसालौ ।</p><p><br></p><p>-अध्यक्ष&nbsp;</p>",
      newsDate: "2079-06-03",
      newsTime: "10:36 AM",
      imageUrl:
        "https://watersoft.com.np/WaterTariffSystem-web/documentUpload/news_89_437-pc-1663563418321.jpg?pfdrid_c=true",
      createdBy: "<EMAIL>",
      galleryUrls: [
        "https://watersoft.com.np/WaterTariffSystem-web/documentUpload/newsGallery_89_437-pc-1683698545967.JPG?pfdrid_c=true",
      ],
      headerNp: "",
      messageNp: "",
      newsDateEn: "2022-09-19",
      type: "notice",
    },
  ],
};
// Create a function to strip HTML tags
const stripHtml = (html) => {
  const temp = document.createElement("div");
  temp.innerHTML = html;
  return temp.textContent || temp.innerText || "";
};

// whatsNew.value = stripHtml(whatsNewData.aboutUsNewsAndImg[0]["message"]);

const images = ref([]);
async function fetchwhatsNew() {
  try {
    const response = await api.get(`/customer/newsAndNotice/${ORG_ID}/notice`);
    console.log(response);
    whatsNew.value = stripHtml(response.data.aboutUsNewsAndImg[0]["message"]);
  } catch (error) {
    console.error("Failed to load gallery:", error);
    // gallerySections.value = [];
  }
}
onMounted(async () => {
  fetchwhatsNew();

  try {
    const response = await api.get(`/customer/sliderImages/${ORG_ID}`);
    if (response.data.status === 1) {
      images.value = response.data.imagesList;
    }
  } catch (error) {
    console.log(error);
  }

  // Initialize slider after images are loaded
  setTimeout(() => {
    initSlider();
  }, 100);
});

onBeforeUnmount(() => {
  // Clear interval when component is unmounted
  if (sliderInterval) {
    clearInterval(sliderInterval);
  }
});

/**
 * Initialize the image slider
 */
function initSlider() {
  const sliderDots = document.querySelectorAll(".slider-dot");
  const sliderItems = document.querySelectorAll(".slider-item");
  const prevButton = document.querySelector(".slider-arrow-left");
  const nextButton = document.querySelector(".slider-arrow-right");

  if (sliderDots.length === 0 || sliderItems.length === 0) return;

  // Function to change slide
  const changeSlide = (index) => {
    // Remove active class from all dots and items
    sliderDots.forEach((d) => d.classList.remove("active"));
    sliderItems.forEach((item) => {
      item.classList.remove("active");
      item.classList.remove("slide-in-right");
      item.classList.remove("slide-in-left");
    });

    // Add active class to current dot and item
    sliderDots[index].classList.add("active");

    // Add animation class based on direction
    if (
      index > currentIndex.value ||
      (currentIndex.value === sliderItems.length - 1 && index === 0)
    ) {
      sliderItems[index].classList.add("slide-in-right");
    } else {
      sliderItems[index].classList.add("slide-in-left");
    }

    sliderItems[index].classList.add("active");

    // Update current index
    currentIndex.value = index;

    // Reset the interval
    if (sliderInterval) {
      clearInterval(sliderInterval);
    }
    startAutoSlide();
  };

  // Set up click events for slider dots
  sliderDots.forEach((dot, index) => {
    dot.addEventListener("click", () => {
      changeSlide(index);
    });
  });

  // Set up click events for navigation arrows
  if (prevButton) {
    prevButton.addEventListener("click", () => {
      const newIndex = (currentIndex.value - 1 + sliderItems.length) % sliderItems.length;
      changeSlide(newIndex);
    });
  }

  if (nextButton) {
    nextButton.addEventListener("click", () => {
      const newIndex = (currentIndex.value + 1) % sliderItems.length;
      changeSlide(newIndex);
    });
  }

  // Function to start auto-sliding
  function startAutoSlide() {
    sliderInterval = setInterval(() => {
      const newIndex = (currentIndex.value + 1) % sliderDots.length;
      changeSlide(newIndex);
    }, 6000); // Slightly longer interval for better user experience
  }

  // Start auto-sliding
  startAutoSlide();
}
</script>

<style scoped>
/* Slider styles */
.slider-section {
  margin-bottom: 0;
}

.slider-container {
  position: relative;
  overflow: hidden;
  height: 500px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
  border-radius: 0;
  border-bottom: none;
}

.slider-item {
  position: absolute;
  opacity: 0;
  width: 100%;
  height: 100%;
  transition: opacity 0.8s ease-in-out;
  z-index: 1;
  overflow: hidden;
}

.slider-item.active {
  opacity: 1;
  z-index: 2;
}

.slider-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    to right,
    rgba(0, 0, 0, 0.7) 0%,
    rgba(0, 0, 0, 0.4) 50%,
    rgba(0, 0, 0, 0.1) 100%
  );
  z-index: 1;
  opacity: 0;
  transition: opacity 1s ease;
}

.slider-item.active::before {
  opacity: 1;
}

.slider-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transform: scale(1.1);
  transition: transform 8s ease-in-out;
  filter: brightness(0.9);
}

.slider-item.active img {
  transform: scale(1);
  filter: brightness(1);
}

/* Slide content animations */
.slide-content {
  opacity: 0;
  transform: translateY(20px);
}

.slider-item.active .slide-content {
  animation: fadeInUp 1s forwards 0.3s;
}

.slide-title {
  opacity: 0;
  transform: translateY(20px);
  position: relative;
  overflow: hidden;
  display: inline-block;
}

.slider-item.active .slide-title {
  animation: revealText 1.2s forwards 0.3s;
}

.slide-title::after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background: #1e40af;
  animation: textRevealBlock 1.5s 0.5s forwards cubic-bezier(0.85, 0, 0.15, 1);
  transform: translateX(-100%);
}

.slider-item.active .slide-title::after {
  animation: textRevealBlock 1.5s 0.5s forwards cubic-bezier(0.85, 0, 0.15, 1);
}

.slide-description {
  opacity: 0;
  transform: translateY(20px);
  position: relative;
  overflow: hidden;
  display: inline-block;
}

.slider-item.active .slide-description {
  animation: revealText 1.2s forwards 0.6s;
}

.slide-description::after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background: #1e40af;
  transform: translateX(-100%);
}

.slider-item.active .slide-description::after {
  animation: textRevealBlock 1.5s 0.8s forwards cubic-bezier(0.85, 0, 0.15, 1);
}

.slide-button {
  opacity: 0;
  transform: scale(0.8);
}

.slider-item.active .slide-button {
  animation: popIn 0.8s forwards 1.2s cubic-bezier(0.17, 0.67, 0.83, 0.67);
}

/* Slide direction animations */
.slide-in-right {
  animation: zoomFadeIn 1s forwards;
}

.slide-in-left {
  animation: zoomFadeIn 1s forwards;
}

/* Navigation dots */
.slider-dots-container {
  display: flex;
  background-color: rgba(0, 0, 0, 0.6);
  padding: 6px 12px;
  border-radius: 30px;
  z-index: 10;
}

.slider-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: transparent;
  border: 2px solid rgba(255, 255, 255, 0.7);
  margin: 0 8px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55);
  position: relative;
}

.slider-dot:hover {
  border-color: white;
  transform: scale(1.2);
}

.slider-dot.active {
  background-color: white;
  border-color: white;
  transform: scale(1.2);
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.slider-dot::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0);
  width: 6px;
  height: 6px;
  background-color: white;
  border-radius: 50%;
  transition: transform 0.3s ease;
}

.slider-dot.active::after {
  transform: translate(-50%, -50%) scale(1);
}

/* Navigation arrows */
.slider-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  opacity: 0.7;
  transition: all 0.3s ease;
}

.slider-arrow:hover {
  background-color: rgba(0, 0, 0, 0.8);
  opacity: 1;
  transform: translateY(-50%) scale(1.1);
}

.slider-arrow-left {
  left: 20px;
}

.slider-arrow-right {
  right: 20px;
}

/* Keyframe animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes revealText {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes textRevealBlock {
  0% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes popIn {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  70% {
    opacity: 1;
    transform: scale(1.1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes zoomFadeIn {
  0% {
    opacity: 0;
    transform: scale(1.1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulseGlow {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(255, 255, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .slider-container {
    height: 350px;
  }

  .slider-arrow {
    width: 32px;
    height: 32px;
  }

  .slider-arrow-left {
    left: 10px;
  }

  .slider-arrow-right {
    right: 10px;
  }
}

@media (max-width: 480px) {
  .slider-container {
    height: 250px;
  }

  .slide-title {
    font-size: 1.5rem;
  }

  .slide-description {
    font-size: 1rem;
  }

  .slide-button {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }
}
</style>
