/* Form validation styles */

/* Valid state */
.form-control.is-valid,
.form-select.is-valid,
.form-check-input.is-valid {
  border-color: var(--color-success, #52c41a);
}

.form-control.is-valid:focus,
.form-select.is-valid:focus,
.form-check-input.is-valid:focus {
  border-color: var(--color-success, #52c41a);
  box-shadow: 0 0 0 0.25rem rgba(82, 196, 26, 0.25);
}

.valid-feedback {
  display: none;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875em;
  color: var(--color-success, #52c41a);
}

.was-validated .form-control:valid ~ .valid-feedback,
.form-control.is-valid ~ .valid-feedback {
  display: block;
}

/* Invalid state */
.form-control.is-invalid,
.form-select.is-invalid,
.form-check-input.is-invalid {
  border-color: var(--color-danger, #ff4d4f);
}

.form-control.is-invalid:focus,
.form-select.is-invalid:focus,
.form-check-input.is-invalid:focus {
  border-color: var(--color-danger, #ff4d4f);
  box-shadow: 0 0 0 0.25rem rgba(255, 77, 79, 0.25);
}

.invalid-feedback {
  display: none;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875em;
  color: var(--color-danger, #ff4d4f);
}

.was-validated .form-control:invalid ~ .invalid-feedback,
.form-control.is-invalid ~ .invalid-feedback,
.was-validated .form-select:invalid ~ .invalid-feedback,
.form-select.is-invalid ~ .invalid-feedback,
.was-validated .form-check-input:invalid ~ .invalid-feedback,
.form-check-input.is-invalid ~ .invalid-feedback {
  display: block;
}

/* Form group spacing */
.form-group {
  margin-bottom: 1.5rem;
}

/* Input with icons */
.with-icon {
  position: relative;
}

.with-icon .form-control {
  padding-left: 40px;
}

.with-icon .input-icon {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 15px;
  z-index: 2;
  color: var(--color-light, #6c757d);
}

/* Input sizes */
.form-control.ih-small {
  height: 38px;
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
}

.form-control.ih-medium {
  height: 48px;
  padding: 0.5rem 1rem;
  font-size: 1rem;
}

.form-control.ih-large {
  height: 60px;
  padding: 0.75rem 1.25rem;
  font-size: 1.125rem;
}

/* Disabled state */
.form-control:disabled,
.form-control.disabled,
.form-select:disabled,
.form-select.disabled,
.form-check-input:disabled,
.form-check-input.disabled {
  background-color: var(--color-disabled-bg, #f5f5f5);
  opacity: 0.65;
  cursor: not-allowed;
}

/* Focus state */
.form-control:focus,
.form-select:focus {
  border-color: var(--color-primary, #1890ff);
  box-shadow: 0 0 0 0.25rem rgba(24, 144, 255, 0.25);
}

/* Placeholder color */
.form-control::placeholder {
  color: var(--color-lighten, #adb5bd);
  opacity: 1;
}
