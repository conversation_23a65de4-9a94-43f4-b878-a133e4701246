<template>
  <section class="committee-members py-16 bg-gray-50">
    <div class="container mx-auto px-4">
      <!-- Section Header -->
      <div class="mb-12 text-center">
        <h2 class="text-3xl md:text-4xl font-bold text-blue-800 mb-2">
          Executive Committee Members
        </h2>
        <div class="w-20 h-1 bg-blue-600 mx-auto"></div>
      </div>

      <!-- Slider -->
      <div class="relative group">
        <!-- Navigation Buttons -->
        <button
          class="absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-white rounded-full shadow-lg p-3 hover:bg-blue-100 transition-all duration-300 opacity-0 group-hover:opacity-100"
          @click="prevSlide"
          :disabled="currentIndex === 0"
          :class="{
            'opacity-30 cursor-not-allowed': currentIndex === 0,
            'hover:scale-110 active:scale-95': currentIndex > 0,
          }"
          aria-label="Previous slide"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-6 w-6 text-blue-800"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M15 19l-7-7 7-7"
            />
          </svg>
        </button>

        <div class="overflow-hidden px-2">
          <div
            class="flex transition-transform duration-500 ease-out"
            :style="{ transform: `translateX(-${currentIndex * (100 / slidesToShow)}%)` }"
          >
            <div
              v-for="(member, idx) in allComiiteeMemberList"
              :key="member.fullName + idx"
              class="committee-member flex-shrink-0 px-3 transition-all duration-300 hover:scale-105"
              :style="{
                width: `${100 / slidesToShow}%`,
                'transition-delay': `${idx * 50}ms`,
              }"
            >
              <div
                class="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-xl transition-shadow duration-300 h-full flex flex-col"
              >
                <div class="relative overflow-hidden group">
                  <img
                    :src="member.imgPath"
                    :alt="member.fullName"
                    class="w-full h-auto object-cover aspect-square transition-transform duration-500 group-hover:scale-110"
                  />
                  <div
                    class="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                  ></div>
                </div>
                <div class="p-4 text-center flex-grow">
                  <h3 class="text-lg font-semibold text-blue-800 mb-1">
                    {{ member.fullName }}
                  </h3>
                  <p class="text-sm text-gray-600">{{ member.desigName }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <button
          class="absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-white rounded-full shadow-lg p-3 hover:bg-blue-100 transition-all duration-300 opacity-0 group-hover:opacity-100"
          @click="nextSlide"
          :disabled="currentIndex >= maxIndex"
          :class="{
            'opacity-30 cursor-not-allowed': currentIndex >= maxIndex,
            'hover:scale-110 active:scale-95': currentIndex < maxIndex,
          }"
          aria-label="Next slide"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-6 w-6 text-blue-800"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M9 5l7 7-7 7"
            />
          </svg>
        </button>
      </div>

      <!-- Dots Indicator -->
      <div class="flex justify-center mt-8 space-x-2">
        <button
          v-for="(dot, index) in dotCount"
          :key="index"
          @click="currentIndex = index * slidesToShow"
          class="w-3 h-3 rounded-full transition-all duration-300"
          :class="{
            'bg-blue-800 w-6': Math.floor(currentIndex / slidesToShow) === index,
            'bg-gray-300 hover:bg-gray-400':
              Math.floor(currentIndex / slidesToShow) !== index,
          }"
          :aria-label="'Go to slide ' + (index + 1)"
        ></button>
      </div>
    </div>
    <!-- <div v-else class="text-center py-12 text-gray-500 text-lg font-medium">
      <span class="inline-block bg-white px-6 py-4 rounded-lg shadow-md">
      No data found
      </span>
    </div> -->
  </section>
</template>

<script setup>
import {
  computed,
  onMounted,
  onUnmounted,
  ref,
} from 'vue'

import { useMemberStore } from '@/stores/memberStore'

const memberStore = useMemberStore();
const allComiiteeMemberList = ref(null);

// Fetch committee data on mount
onMounted(async () => {
  await memberStore.fetchCommittees();
  allComiiteeMemberList.value = await memberStore.fetchcomitteMember();
});

const allBoardmemberImages = computed(
  () => memberStore.getCommitteeMemberImages("Board Members") || []
);

// Responsive slides configuration
const slidesToShow = ref(6);

function updateSlidesToShow() {
  if (window.innerWidth < 640) slidesToShow.value = 2;
  else if (window.innerWidth < 768) slidesToShow.value = 3;
  else if (window.innerWidth < 1024) slidesToShow.value = 4;
  else slidesToShow.value = 6;
}

onMounted(() => {
  updateSlidesToShow();
  window.addEventListener("resize", updateSlidesToShow);
});

const currentIndex = ref(0);
const maxIndex = computed(() =>
  Math.max(0, allBoardmemberImages.value.length - slidesToShow.value)
);

// Calculate number of dots needed
const dotCount = computed(() => {
  return Math.ceil(allBoardmemberImages.value.length / slidesToShow.value);
});

function prevSlide() {
  currentIndex.value = Math.max(0, currentIndex.value - 1);
}

function nextSlide() {
  if (currentIndex.value >= maxIndex.value) {
    currentIndex.value = 0; // Loop back to start
  } else {
    currentIndex.value += 1;
  }
}

// Autoplay with pause on hover
let autoplayInterval = null;
const sliderContainer = ref(null);

const startAutoplay = () => {
  autoplayInterval = setInterval(() => {
    nextSlide();
  }, 3000);
};

const pauseAutoplay = () => {
  clearInterval(autoplayInterval);
};

onMounted(() => {
  startAutoplay();
  if (sliderContainer.value) {
    sliderContainer.value.addEventListener("mouseenter", pauseAutoplay);
    sliderContainer.value.addEventListener("mouseleave", startAutoplay);
  }
});

onUnmounted(() => {
  pauseAutoplay();
  window.removeEventListener("resize", updateSlidesToShow);
  if (sliderContainer.value) {
    sliderContainer.value.removeEventListener("mouseenter", pauseAutoplay);
    sliderContainer.value.removeEventListener("mouseleave", startAutoplay);
  }
});
</script>

<style scoped>
.committee-member {
  transition: transform 0.3s ease, opacity 0.3s ease;
}

/* Smooth transitions for the slider */
.slider-transition {
  transition: transform 0.6s cubic-bezier(0.25, 0.1, 0.25, 1);
}

/* Fade-in effect for buttons */
.nav-button {
  transition: opacity 0.3s ease, transform 0.2s ease;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .committee-member {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
}
</style>
