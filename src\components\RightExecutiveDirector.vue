<script setup>
import {
  computed,
  onMounted,
} from 'vue'

import { useMemberStore } from '@/stores/memberStore'

const memberStore = useMemberStore();

onMounted(async () => {
  await memberStore.fetchCommittees();
  console.log(memberStore.getCommittees);
  console.log(memberStore.getCommitteeMemberImages("Board Member", 2))
});

const memberDetails = computed(() =>
  memberStore.getCommitteeMemberImages("Board Members", 2)
);
</script>
<template>
  <div v-if="memberDetails.length === 0">
   No data
  </div>
  <div v-else class="lg:col-span-1">
    <div
      class="bg-white rounded-xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl"
    >
      <!-- Executive Director Profile -->
      <div class="p-6 border-b border-gray-200" v-for="member in memberDetails">
        <div class="flex flex-col items-center">
          <div
            class="relative w-32 h-32 mb-4 overflow-hidden rounded-full border-4 border-blue-100 shadow-inner"
          >
            <img
              :src="member.image"
              alt="Mr. Prajapati Dalal"
              class="w-full h-full object-cover"
            />
          </div>
          <h4 class="text-xl font-bold text-blue-900">{{ member.name }}</h4>
          <p class="text-blue-600 font-medium">{{ member.staffDesignation }}</p>
          <div class="mt-2 w-full">
            <div class="flex items-center justify-center space-x-3">
              <span
                class="inline-flex items-center px-3 py-1 rounded-full bg-blue-50 text-blue-700 font-semibold text-sm shadow-sm border border-blue-200"
              >
                <svg
                  class="w-4 h-4 mr-1 text-blue-400"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M3 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H5a2 2 0 01-2-2V5zm0 12a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H5a2 2 0 01-2-2v-2zm12-12a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zm0 12a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"
                  />
                </svg>
                {{ member.staffContactNumber1 }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
