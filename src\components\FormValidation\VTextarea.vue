<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { validateValue } from '@/utils/validation'

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  label: {
    type: String,
    default: ''
  },
  name: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: ''
  },
  rows: {
    type: [Number, String],
    default: 4
  },
  validations: {
    type: Array,
    default: () => []
  },
  formValues: {
    type: Object,
    default: () => ({})
  },
  validateOnBlur: {
    type: Boolean,
    default: true
  },
  validateOnChange: {
    type: Boolean,
    default: false
  },
  validateOnMount: {
    type: Boolean,
    default: false
  },
  customClass: {
    type: String,
    default: ''
  },
  disabled: {
    type: Boolean,
    default: false
  },
  readonly: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'validation', 'blur', 'focus', 'input'])

const textareaValue = computed({
  get: () => props.modelValue,
  set: (value) => {
    emit('update:modelValue', value)
    if (props.validateOnChange) {
      validate()
    }
  }
})

const errors = ref([])
const touched = ref(false)
const validating = ref(false)

const isValid = computed(() => errors.value.length === 0)
const showError = computed(() => touched.value && !isValid.value)

const textareaClasses = computed(() => {
  return {
    'form-control': true,
    'is-invalid': showError.value,
    'is-valid': touched.value && isValid.value,
    [props.customClass]: !!props.customClass,
    'disabled': props.disabled
  }
})

const validate = async () => {
  validating.value = true
  const { errors: validationErrors } = await validateValue(
    textareaValue.value,
    props.validations,
    props.formValues
  )
  errors.value = validationErrors
  emit('validation', { valid: isValid.value, errors: errors.value, field: props.name })
  validating.value = false
  return isValid.value
}

const handleBlur = (event) => {
  touched.value = true
  if (props.validateOnBlur) {
    validate()
  }
  emit('blur', event)
}

const handleFocus = (event) => {
  emit('focus', event)
}

const handleInput = (event) => {
  emit('input', event)
}

watch(() => props.formValues, () => {
  if (touched.value) {
    validate()
  }
}, { deep: true })

onMounted(() => {
  if (props.validateOnMount) {
    touched.value = true
    validate()
  }
})

defineExpose({ validate, errors, isValid })
</script>

<template>
  <div class="form-group">
    <label v-if="label" :for="name">{{ label }}</label>
    
    <textarea
      :id="name"
      :name="name"
      :class="textareaClasses"
      :placeholder="placeholder"
      :rows="rows"
      v-model="textareaValue"
      :disabled="disabled"
      :readonly="readonly"
      @blur="handleBlur"
      @focus="handleFocus"
      @input="handleInput"
    ></textarea>
    
    <div v-if="showError" class="invalid-feedback">
      {{ errors[0] }}
    </div>
  </div>
</template>

<style scoped>
.form-group {
  margin-bottom: 1.5rem;
}
</style>
