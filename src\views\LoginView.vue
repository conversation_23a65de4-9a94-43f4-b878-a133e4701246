<script setup>
import { ref } from 'vue'

import { useRouter } from 'vue-router'

import {
  VForm,
  VInput,
} from '@/components/FormValidation'
import useAuthStore from '@/stores/authStore'
import { rules } from '@/utils/validation'

const router = useRouter();

// Form data
const formData = ref({
    username: '',
    password: ''
})

// Validation rules
const validationRules = {
    username: [rules.required],
    password: [rules.required, rules.minLength(6)]
}

// Handle form submission
const handleSubmit = async ({ values, valid }) => {
    if (valid) {
        // Proceed with login if validation passes
        const authStore = useAuthStore()
        const isLoggedIn = await authStore.login(values);
        if (isLoggedIn == true) {
            router.push({ name: 'home' });
        }
    } else {
        // Show error notification
        if (window.toastr) {
            window.toastr.error('Please fix the errors in the form', 'Error', {
                position: 'top-right',
                duration: 5000
            })
        }
    }
}
</script>
<template>
    <main class="main-content">
        <div class="admin">
            <div class="container-fluid">
                <div class="row justify-content-center">
                    <div class="col-xxl-3 col-xl-4 col-md-6 col-sm-8">
                        <div class="edit-profile">
                            <div class="edit-profile__logos">
                                <a href="index.html">
                                    <!-- <img class="dark" src="/img/logo-dark.png" alt="">
                                    <img class="light" src="/img/logo-white.png" alt=""> -->
                                    <img class="dark" src="/img/bussewaLogo.png" alt="logo">

                                </a>
                            </div>
                            <div class="card border-0">
                                <div class="card-header">
                                    <div class="edit-profile__title">
                                        <h6>Sign in HexaDash</h6>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <!-- <form action=""> -->
                                    <VForm :initial-values="formData" :validation-rules="validationRules"
                                        @submit="handleSubmit">
                                        <template #default="{ values, errors, isSubmitting }">
                                            <div class="edit-profile__body">
                                                <div class="form-group mb-25">
                                                    <VInput v-model="values.username" name="username" label="Username"
                                                        placeholder="Enter Username"
                                                        :validations="validationRules.username" :form-values="values" />
                                                </div>
                                                <div class="form-group mb-15">
                                                    <VInput v-model="values.password" name="password" label="Password"
                                                        type="password" placeholder="Enter Password"
                                                        :validations="validationRules.password" :form-values="values" />
                                                </div>
                                                <div class="admin-condition">
                                                    <div class="checkbox-theme-default custom-checkbox ">
                                                        <input class="checkbox" type="checkbox" id="check-1">
                                                        <label for="check-1">
                                                            <span class="checkbox-text">Keep me logged in</span>
                                                        </label>
                                                    </div>
                                                    <a href="forget-password.html">forget password?</a>
                                                </div>
                                                <div
                                                    class="admin__button-group button-group d-flex pt-1 justify-content-md-start justify-content-center">
                                                    <button type="submit"
                                                        class="btn btn-primary btn-default w-100 btn-squared text-capitalize lh-normal px-50 signIn-createBtn"
                                                        :disabled="isSubmitting">
                                                        {{ isSubmitting ? 'Signing In...' : 'Sign In' }}
                                                    </button>
                                                </div>
                                            </div>
                                        </template>
                                    </VForm>
                                </div>
                            </div>

                            <div class="admin-topbar">
                                <p class="mb-0">
                                    Don't have an account?
                                    <!-- <a href="sign-up.html" class="color-primary">
                                            Sign up
                                        </a> -->
                                </p>
                            </div>
                            <!-- End: .admin-topbar  -->
                        </div><!-- End: .card -->
                    </div><!-- End: .edit-profile -->
                </div><!-- End: .col-xl-5 -->
            </div>
        </div>
    </main>
    <div id="overlayer">
        <div class="loader-overlay">
            <div class="dm-spin-dots spin-lg">
                <span class="spin-dot badge-dot dot-primary"></span>
                <span class="spin-dot badge-dot dot-primary"></span>
                <span class="spin-dot badge-dot dot-primary"></span>
                <span class="spin-dot badge-dot dot-primary"></span>
            </div>
        </div>
    </div>
    <div class="enable-dark-mode dark-trigger">
        <ul>
            <li>
                <a href="#">
                    <i class="uil uil-moon"></i>
                </a>
            </li>
        </ul>
    </div>
</template>