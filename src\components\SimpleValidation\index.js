import VCheckbox from './VCheckbox.vue'
import VInput from './VInput.vue'
import VRadio from './VRadio.vue'
import VSelect from './VSelect.vue'
import VTextarea from './VTextarea.vue'

export { VCheckbox, VInput, VRadio, VSelect, VTextarea }

export default {
  install(app) {
    app.component('VInput', VInput)
    app.component('VSelect', VSelect)
    app.component('VCheckbox', VCheckbox)
    app.component('VTextarea', VTextarea)
    app.component('VRadio', VRadio)
  }
}
