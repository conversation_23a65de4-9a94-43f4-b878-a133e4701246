<script setup>
import {
  onBeforeMount,
  onMounted,
  ref,
} from 'vue'

import FooterComponent from '@/components/frontend/FooterComponent.vue'
import HeaderComponent from '@/components/frontend/HeaderComponent.vue'
import NavigationComponent from '@/components/frontend/NavigationComponent.vue'

const isLoading = ref(true)
const isExiting = ref(false)

// Set loading state before mounting
onBeforeMount(() => {
  // Ensure body has the right class for preloader
  document.body.classList.add('overlayScroll')
  document.body.classList.remove('loaded')
})

// Import Tailwind CSS and initialize functionality
onMounted(() => {
  console.log('Preloader active:', isLoading.value)
  
  // Start exit animation after a shorter delay
  setTimeout(() => {
    startExitAnimation();
  }, 500); // Reduced from 2500ms to 1500ms

  // Add Tailwind CSS script
  const tailwindScript = document.createElement("script");
  tailwindScript.src = "https://cdn.tailwindcss.com";
  document.head.appendChild(tailwindScript);

  // Add Tailwind configuration
  const tailwindConfig = document.createElement("script");
  tailwindConfig.textContent = `
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#001755', // dark blue (navigation color)
            secondary: '#0ea5e9', // light blue
            accent: '#f59e0b', // amber
          }
        }
      }
    }
  `;
  document.head.appendChild(tailwindConfig);

  // Add Font Awesome
  const fontAwesome = document.createElement("link");
  fontAwesome.rel = "stylesheet";
  fontAwesome.href =
    "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css";
  document.head.appendChild(fontAwesome);

  // Initialize mobile menu functionality
  setTimeout(() => {
    initMobileMenu();
    initMobileDropdowns();
    initSlider();
  }, 500);
});

// Function to start exit animation
function startExitAnimation() {
  isExiting.value = true;
  
  // Complete hide after animation finishes
  setTimeout(() => {
    hidePreloader();
  }, 800); // Reduced from 1000ms to 800ms
}

// Function to hide preloader
function hidePreloader() {
  isLoading.value = false;
  document.body.classList.add('loaded');
  document.body.classList.remove('overlayScroll');
}

// Initialize mobile menu toggle functionality
function initMobileMenu() {
  const mobileMenuButton = document.getElementById("mobile-menu-button");
  const mobileMenu = document.getElementById("mobile-menu");

  if (mobileMenuButton && mobileMenu) {
    mobileMenuButton.addEventListener("click", () => {
      mobileMenu.classList.toggle("hidden");
    });
  }
}

// Initialize mobile dropdown menus
function initMobileDropdowns() {
  const mobileDropdowns = document.querySelectorAll(".mobile-dropdown");

  mobileDropdowns.forEach((dropdown) => {
    const button = dropdown.querySelector("button");
    const content = dropdown.querySelector("div:not(:first-child)");

    if (button && content) {
      button.addEventListener("click", () => {
        content.classList.toggle("hidden");

        // Toggle the chevron icon
        const icon = button.querySelector(".fa-chevron-down");
        if (icon) {
          icon.classList.toggle("fa-chevron-down");
          icon.classList.toggle("fa-chevron-up");
        }
      });
    }
  });
}

// Initialize the image slider
function initSlider() {
  const sliderDots = document.querySelectorAll(".slider-dot");
  const sliderItems = document.querySelectorAll(".slider-item");

  if (sliderDots.length === 0 || sliderItems.length === 0) return;

  // Set up click events for slider dots
  sliderDots.forEach((dot, index) => {
    dot.addEventListener("click", () => {
      // Remove active class from all dots and items
      sliderDots.forEach((d) => d.classList.remove("active"));
      sliderItems.forEach((item) => item.classList.remove("active"));

      // Add active class to current dot and item
      dot.classList.add("active");
      if (sliderItems[index]) {
        sliderItems[index].classList.add("active");
      }
    });
  });

  // Auto-rotate slider every 5 seconds
  let currentIndex = 0;
  setInterval(() => {
    currentIndex = (currentIndex + 1) % sliderDots.length;
    sliderDots[currentIndex].click();
  }, 5000);
}
</script>

<template>
  <!-- Enhanced Water Drop Preloader with Split Exit Animation -->
  <div id="overlayer" v-show="isLoading" :class="{ 'exiting': isExiting }">
    <div class="split-container">
      <div class="split-half left-half"></div>
      <div class="split-half right-half"></div>
    </div>
    <div class="loader-overlay">
      <div class="water-preloader">
        <div class="water-drop primary-drop"></div>
        <div class="water-drop secondary-drop"></div>
        <div class="water-drop tertiary-drop"></div>
        <div class="water-ripple"></div>
        <div class="water-ripple ripple-2"></div>
        <div class="water-ripple ripple-3"></div>
        <div class="water-surface"></div>
      </div>
    </div>
  </div>

  <div class="frontend-layout bg-gray-100">
    <!-- Header Component -->
    <HeaderComponent />

    <!-- Navigation Component -->
    <NavigationComponent />

    <!-- Main Content -->
    <router-view></router-view>

    <!-- Footer Component -->
    <FooterComponent />
  </div>
</template>

<style>
/* Enhanced Water Drop Preloader styles */
#overlayer {
  width: 100%;
  height: 100%;
  position: fixed;
  z-index: 99999;
  background: linear-gradient(135deg, rgba(0, 23, 85, 0.9) 0%, rgba(14, 165, 233, 0.85) 100%);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  top: 0;
  left: 0;
  max-height: 100vh;
}

.loader-overlay {
  position: absolute;
  z-index: 99999;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.water-preloader {
  position: relative;
  width: 150px;
  height: 150px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.water-drop {
  position: absolute;
  width: 20px;
  height: 20px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 50% 50% 50% 0;
  transform: rotate(-45deg);
  filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.7));
}

.primary-drop {
  animation: dropFall 1.5s ease-in-out infinite;
}

.secondary-drop {
  width: 15px;
  height: 15px;
  animation: dropFall 1.5s ease-in-out infinite 0.3s;
  opacity: 0.8;
}

.tertiary-drop {
  width: 12px;
  height: 12px;
  animation: dropFall 1.5s ease-in-out infinite 0.6s;
  opacity: 0.6;
}

.water-ripple {
  position: absolute;
  bottom: 30px;
  width: 15px;
  height: 15px;
  border: 2px solid rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  animation: rippleEffect 1.5s ease-out infinite;
  opacity: 0;
}

.ripple-2 {
  animation-delay: 0.3s;
}

.ripple-3 {
  animation-delay: 0.6s;
}

.water-surface {
  position: absolute;
  bottom: 30px;
  width: 80px;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  filter: blur(1px);
}

.water-preloader p {
  color: white;
  font-weight: 600;
  letter-spacing: 1px;
  margin-top: 90px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

@keyframes dropFall {
  0% {
    top: 0;
    opacity: 1;
    transform: rotate(-45deg) scale(1);
  }
  70% {
    top: 70px;
    opacity: 1;
    transform: rotate(-45deg) scale(1);
  }
  80% {
    top: 70px;
    transform: rotate(-45deg) scale(0.2);
    opacity: 0;
  }
  100% {
    top: 70px;
    transform: rotate(-45deg) scale(0);
    opacity: 0;
  }
}

@keyframes rippleEffect {
  0% {
    width: 0;
    height: 0;
    opacity: 0.8;
  }
  100% {
    width: 80px;
    height: 80px;
    opacity: 0;
  }
}

/* Hide preloader when loaded */
body.loaded #overlayer {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
}

.overlayScroll {
  overflow: hidden;
}

/* Your existing styles */
.nav-item {
  padding: 0.5rem 1rem;
  color: white;
  transition: background-color 0.3s;
}

.nav-item:hover {
  background-color: #1e40af; 
}

.marquee {
  white-space: nowrap;
  overflow: hidden;
}

.marquee span {
  display: inline-block;
  padding-left: 100%;
  animation: marquee 30s linear infinite;
}

@keyframes marquee {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(-100%, 0);
  }
}

/* Enhanced Animated Water-themed Preloader styles with Split Exit */
#overlayer {
  width: 100%;
  height: 100%;
  position: fixed;
  z-index: 99999;
  top: 0;
  left: 0;
  max-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  animation: fadeIn 0.6s ease-out forwards;
}

/* Split container and halves */
.split-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.split-half {
  position: absolute;
  top: 0;
  height: 100%;
  width: 50%;
  background: linear-gradient(135deg, rgba(0, 23, 85, 0.9) 0%, rgba(14, 165, 233, 0.85) 100%);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  transition: transform 0.8s cubic-bezier(0.7, 0, 0.3, 1);
}

.left-half {
  left: 0;
}

.right-half {
  right: 0;
}

/* Exit animation classes */
#overlayer.exiting .left-half {
  transform: translateX(-100%);
}

#overlayer.exiting .right-half {
  transform: translateX(100%);
}

#overlayer.exiting .water-preloader {
  animation: scaleDown 0.5s forwards;
}

/* Hide preloader when loaded */
body.loaded #overlayer {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
}

.overlayScroll {
  overflow: hidden;
}

/* Entry and Exit Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes scaleDown {
  from { transform: scale(1); opacity: 1; }
  to { transform: scale(0.5); opacity: 0; }
}
</style>
