export default [
    {
        path: 'dashboard',
        name: 'home',
        component: () => import('@/views/DashboardContent.vue'),
        meta: { isAdmin: true },
    },
    {
        path: 'about',
        name: 'about',
        component: () => import('@/views/AboutContent.vue'),
        meta: { isAdmin: true },

    },
    {
        path: 'customer-details',
        name: 'customer-details',
        component: () => import('@/views/CustomerDetails.vue'),
        meta: { isAdmin: true },

    },
    {
        path: 'admissions-table',
        name: 'admissions-table',
        component: () => import('@/views/AdmissionsTableExample.vue'),
        meta: { isAdmin: true },

    },
];