<script setup>
import AppLayout from '../components/AppLayout.vue'
import SidebarMenu from '../components/SidebarMenu.vue'
import { menuItems } from '../config/menuConfig.js'
</script>

<template>
  <AppLayout>
    <template #top-menu>
      <div class="top-menu">
        <div class="hexadash-top-menu position-relative">
          <ul class="d-flex align-items-center flex-wrap">
            <li class="has-subMenu">
              <a href="#" class="">Dashboard</a>
            </li>
            <li class="has-subMenu">
              <a href="#" class="">Layouts</a>
            </li>
          </ul>
        </div>
      </div>
    </template>

    <template #sidebar>
      <SidebarMenu :menuItems="menuItems" />
    </template>

    <template #navbar-right>
      <div class="navbar-right">
        <ul class="navbar-right__menu">
          <li class="nav-search">
            <a href="#" class="search-toggle">
              <i class="uil uil-search"></i>
              <i class="uil uil-times"></i>
            </a>
          </li>
        </ul>
      </div>
    </template>

    <!-- This router-view will render the child route components -->
    <router-view></router-view>
  </AppLayout>
</template>
