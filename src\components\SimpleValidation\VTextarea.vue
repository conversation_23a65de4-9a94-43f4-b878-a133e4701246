<script setup>
import {
  computed,
  ref,
} from 'vue'

const props = defineProps({
  field: {
    type: Object,
    required: true
  },
  label: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: ''
  },
  id: {
    type: String,
    default: ''
  },
  rows: {
    type: [Number, String],
    default: 4
  },
  validateOnInput: {
    type: Boolean,
    default: false
  }
})

// Only show errors after the user has interacted with the field and moved away
const localTouched = ref(false)

const handleBlur = () => {
  localTouched.value = true
  props.field.setTouched()
}

const handleInput = () => {
  if (props.validateOnInput && localTouched.value) {
    props.field.setTouched()
  }
}

const classes = computed(() => ({
  'v-form-group': true,
  'has-error': localTouched.value && props.field.hasError.value
}))
</script>

<template>
  <div :class="classes">
    <label v-if="label" :for="id">{{ label }}</label>
    <textarea :id="id" :placeholder="placeholder" :rows="rows" v-model="field.value" @blur="handleBlur"
      @input="handleInput"></textarea>
    <div v-if="localTouched && field.hasError.value" class="error-message">
      {{ field.errors.value[0] }}
    </div>
  </div>
</template>
