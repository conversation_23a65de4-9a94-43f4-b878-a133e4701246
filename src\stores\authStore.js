import { defineStore } from 'pinia'

import api from '@/api/api'

const useAuthStore = defineStore('authStore', {
    state: () => ({
        userInfo: localStorage.getItem('user') ? JSON.parse(localStorage.getItem('user')) : null,
        isAuthenticated: !!localStorage.getItem('user'),
    }),

    actions: {
        async login(userData) {
            try {
                const response = await api.post('/auth/login', userData)

                const data = {
                    access_token: response.data.accessToken.token,
                    access_expires_at: response.data.accessToken.expiresIn,
                    refresh_token: response.data.refreshToken.token,
                    refresh_expires_at: response.data.refreshToken.expiresIn,
                    username: response.data.username,
                    name: response.data.name,
                }

                localStorage.setItem('user', JSON.stringify(data))
                this.userInfo = data
                this.isAuthenticated = true

                toastr.success('Login successful!', 'Success', {
                    position: 'top-right',
                    duration: 5000
                })

                return true
            } catch (error) {
                console.error('<PERSON><PERSON> failed:', error)

                toastr.error('<PERSON><PERSON> failed! Check credentials.', 'Error', {
                    position: 'top-right',
                    duration: 5000
                })

                return false
            }
        },

        logout() {
            localStorage.removeItem('user')
            this.userInfo = null
            this.isAuthenticated = false
            window.location.href = '/login'
        }
    }
})

export default useAuthStore
