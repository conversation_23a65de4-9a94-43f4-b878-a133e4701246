/**
 * Enhances code blocks with copy functionality
 */
document.addEventListener('DOMContentLoaded', () => {
  // Find all code blocks
  const codeBlocks = document.querySelectorAll('.code-block pre code')
  
  // Add copy button to each code block
  codeBlocks.forEach(codeBlock => {
    // Create copy button
    const copyButton = document.createElement('button')
    copyButton.className = 'copy-button'
    copyButton.textContent = 'Copy'
    
    // Add copy functionality
    copyButton.addEventListener('click', () => {
      // Get code content
      const code = codeBlock.textContent
      
      // Copy to clipboard
      navigator.clipboard.writeText(code)
        .then(() => {
          // Show success message
          copyButton.textContent = 'Copied!'
          
          // Reset button text after 2 seconds
          setTimeout(() => {
            copyButton.textContent = 'Copy'
          }, 2000)
        })
        .catch(err => {
          console.error('Failed to copy code: ', err)
          copyButton.textContent = 'Error!'
          
          // Reset button text after 2 seconds
          setTimeout(() => {
            copyButton.textContent = 'Copy'
          }, 2000)
        })
    })
    
    // Add button to code block
    codeBlock.parentNode.appendChild(copyButton)
  })
})
