/**
 * Component loader for the Water Supply Management Board website
 * This file handles loading HTML components into the main page
 */

document.addEventListener('DOMContentLoaded', function() {
    // Load all components
    loadComponents();

    // Initialize mobile menu functionality after components are loaded
    setTimeout(() => {
        initMobileMenu();
        initMobileDropdowns();
    }, 100);
});

/**
 * Load all HTML components into their respective containers
 */
async function loadComponents() {
    const components = [
        { id: 'header-container', file: 'components/header.html' },
        { id: 'nav-container', file: 'components/navigation.html' },
        { id: 'slider-container', file: 'components/slider.html' },
        { id: 'whats-new-container', file: 'components/whats-new.html' },
        { id: 'main-content-container', file: 'components/main-content.html' },
        { id: 'footer-container', file: 'components/footer.html' }
    ];

    // Load each component
    for (const component of components) {
        try {
            const response = await fetch(component.file);
            if (!response.ok) {
                throw new Error(`Failed to load ${component.file}`);
            }
            const html = await response.text();
            document.getElementById(component.id).innerHTML = html;
        } catch (error) {
            console.error(`Error loading component: ${error.message}`);
        }
    }

    // Initialize slider after components are loaded
    initSlider();
}

/**
 * Initialize mobile menu toggle functionality
 */
function initMobileMenu() {
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');

    if (mobileMenuButton && mobileMenu) {
        mobileMenuButton.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
        });
    }
}

/**
 * Initialize mobile dropdown menus
 */
function initMobileDropdowns() {
    const mobileDropdowns = document.querySelectorAll('.mobile-dropdown');

    mobileDropdowns.forEach(dropdown => {
        const button = dropdown.querySelector('button');
        const content = dropdown.querySelector('div:not(:first-child)');

        if (button && content) {
            button.addEventListener('click', () => {
                content.classList.toggle('hidden');

                // Toggle the chevron icon
                const icon = button.querySelector('.fa-chevron-down');
                if (icon) {
                    icon.classList.toggle('fa-chevron-down');
                    icon.classList.toggle('fa-chevron-up');
                }
            });
        }
    });
}
