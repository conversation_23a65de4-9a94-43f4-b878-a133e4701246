<script setup>
import { ref, provide, onMounted } from 'vue'
import { validateForm } from '@/utils/validation'

const props = defineProps({
  initialValues: {
    type: Object,
    default: () => ({})
  },
  validationRules: {
    type: Object,
    default: () => ({})
  },
  validateOnMount: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['submit', 'validation', 'reset'])

const formValues = ref({ ...props.initialValues })
const formErrors = ref({})
const isSubmitting = ref(false)
const isValid = ref(true)
const formTouched = ref(false)

// Provide form context to child components
provide('formContext', {
  values: formValues,
  errors: formErrors,
  isSubmitting,
  isValid
})

const validateFormData = async () => {
  const { valid, errors } = await validateForm(formValues.value, props.validationRules)
  formErrors.value = errors
  isValid.value = valid
  emit('validation', { valid, errors })
  return valid
}

const handleSubmit = async (event) => {
  event.preventDefault()
  formTouched.value = true
  isSubmitting.value = true
  
  const valid = await validateFormData()
  
  emit('submit', {
    values: formValues.value,
    valid,
    errors: formErrors.value
  })
  
  isSubmitting.value = false
}

const handleReset = (event) => {
  event.preventDefault()
  formValues.value = { ...props.initialValues }
  formErrors.value = {}
  isValid.value = true
  formTouched.value = false
  emit('reset')
}

const setFieldValue = (field, value) => {
  formValues.value[field] = value
}

const setFieldError = (field, error) => {
  if (!formErrors.value[field]) {
    formErrors.value[field] = []
  }
  formErrors.value[field].push(error)
}

const clearFieldError = (field) => {
  formErrors.value[field] = []
}

onMounted(() => {
  if (props.validateOnMount) {
    formTouched.value = true
    validateFormData()
  }
})

defineExpose({
  validate: validateFormData,
  values: formValues,
  errors: formErrors,
  isValid,
  isSubmitting,
  setFieldValue,
  setFieldError,
  clearFieldError,
  reset: handleReset
})
</script>

<template>
  <form @submit="handleSubmit" @reset="handleReset" novalidate>
    <slot
      :values="formValues"
      :errors="formErrors"
      :is-submitting="isSubmitting"
      :is-valid="isValid"
      :validate="validateFormData"
      :set-field-value="setFieldValue"
      :set-field-error="setFieldError"
      :clear-field-error="clearFieldError"
    ></slot>
  </form>
</template>
