# Dynamic Sidebar Component

This document explains how to use and extend the dynamic sidebar component.

## Overview

The dynamic sidebar component provides a flexible, multi-level navigation menu with the following features:

- Multiple hierarchy levels (up to 3 levels deep)
- Active item highlighting
- Automatic expansion of parent menus when a child is active
- Badge support
- Icon support
- Easy configuration through a menu data structure

## Components

The sidebar implementation consists of the following files:

1. `src/components/SidebarMenu.vue` - The main component that renders the sidebar menu
2. `src/config/menuConfig.js` - Configuration file with menu structure and helper functions
3. `src/assets/sidebar.css` - Custom CSS styles for the sidebar

## How to Use

### Basic Usage

In your view component, import the SidebarMenu component and the menu configuration:

```vue
<script setup>
import SidebarMenu from '../components/SidebarMenu.vue'
import { menuItems } from '../config/menuConfig.js'
</script>

<template>
  <AppLayout>
    <template #sidebar>
      <SidebarMenu :menuItems="menuItems" />
    </template>
    
    <!-- Rest of your content -->
  </AppLayout>
</template>
```

### Adding New Menu Items

To add new menu items, edit the `menuConfig.js` file. Use the provided helper functions to create menu items:

```javascript
// Add a new top-level menu item
createMenuItem('reports', 'Reports', {
  icon: 'uil uil-chart',
  route: '/reports'
})

// Add a menu item with children
createMenuItem('analytics', 'Analytics', {
  icon: 'uil uil-analytics',
  children: [
    createMenuItem('dashboard', 'Dashboard', {
      route: '/analytics/dashboard'
    }),
    createMenuItem('reports', 'Reports', {
      route: '/analytics/reports'
    })
  ]
})

// Add a section title
createTitle('admin', 'Administration')
```

### Menu Item Properties

Each menu item can have the following properties:

- `id` (required): Unique identifier for the menu item
- `label` (required): Display text for the menu item
- `type`: 'title' for section headers, 'item' for regular menu items (default: 'item')
- `icon`: Icon class (using Unicons or other icon libraries)
- `route`: Route path for navigation
- `exact`: Whether the route should match exactly for highlighting (default: true)
- `badge`: Badge to display next to the menu item (object with `text` and `variant` properties)
- `children`: Array of submenu items

### Badge Variants

The following badge variants are available:

- `primary` - Blue
- `success` - Green
- `warning` - Yellow
- `danger` - Red

Example:

```javascript
createMenuItem('notifications', 'Notifications', {
  icon: 'uil uil-bell',
  route: '/notifications',
  badge: {
    text: '5',
    variant: 'danger'
  }
})
```

## Customization

### Styling

To customize the sidebar styles, edit the `src/assets/sidebar.css` file. The main styling classes are:

- `.sidebar_nav` - The main sidebar navigation container
- `.sidebar__menu-group` - The menu group container
- `.has-child` - Menu items with children
- `.active` - Active menu items
- `.menu-title` - Section titles
- `.menuItem` - Badges

### Icons

The sidebar uses the Unicons icon library by default. You can use any icon library by specifying the appropriate class names in the menu configuration.

## Advanced Usage

### Dynamic Menu Generation

You can dynamically generate menu items based on user permissions or other conditions:

```javascript
// Example: Generate menu based on user permissions
const generateMenuItems = (userPermissions) => {
  const items = [
    // Basic items everyone can see
    createMenuItem('dashboard', 'Dashboard', {
      icon: 'uil uil-create-dashboard',
      route: '/'
    })
  ]
  
  // Add admin section if user has admin permissions
  if (userPermissions.includes('admin')) {
    items.push(
      createTitle('admin', 'Administration'),
      createMenuItem('users', 'User Management', {
        icon: 'uil uil-users-alt',
        route: '/admin/users'
      })
    )
  }
  
  return items
}
```

Then use the generated items in your component:

```vue
<script setup>
import { ref, computed } from 'vue'
import SidebarMenu from '../components/SidebarMenu.vue'
import { generateMenuItems } from '../config/menuConfig.js'

const userPermissions = ref(['user', 'admin'])
const menuItems = computed(() => generateMenuItems(userPermissions.value))
</script>

<template>
  <AppLayout>
    <template #sidebar>
      <SidebarMenu :menuItems="menuItems" />
    </template>
  </AppLayout>
</template>
```
