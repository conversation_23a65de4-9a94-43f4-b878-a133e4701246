/* Simple Validator Styles */

/* Input states */
input.is-invalid,
select.is-invalid,
textarea.is-invalid {
  border-color: var(--color-danger, #ff4d4f) !important;
  box-shadow: 0 0 0 1px var(--color-danger, #ff4d4f) !important;
}

input.is-valid,
select.is-valid,
textarea.is-valid {
  border-color: var(--color-success, #52c41a) !important;
  box-shadow: 0 0 0 1px var(--color-success, #52c41a) !important;
}

/* Error message */
.error-message {
  color: var(--color-danger, #ff4d4f);
  font-size: 0.875rem;
  margin-top: 0.25rem;
  display: block;
}

/* Form group */
.form-group {
  margin-bottom: 1.5rem;
  position: relative;
}

/* Animations */
@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
  20%, 40%, 60%, 80% { transform: translateX(5px); }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

input.is-invalid,
select.is-invalid,
textarea.is-invalid {
  animation: shake 0.6s ease-in-out;
}

.error-message {
  animation: fadeIn 0.3s ease-in-out;
}

/* Success animation */
@keyframes success-pulse {
  0% { box-shadow: 0 0 0 0 rgba(82, 196, 26, 0.4); }
  70% { box-shadow: 0 0 0 10px rgba(82, 196, 26, 0); }
  100% { box-shadow: 0 0 0 0 rgba(82, 196, 26, 0); }
}

input.is-valid,
select.is-valid,
textarea.is-valid {
  animation: success-pulse 1.5s ease-in-out;
}
