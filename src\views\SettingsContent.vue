<template>
  <div class="container-fluid">
    <div class="row">
      <div class="col-lg-12">
        <div class="breadcrumb-main">
          <h4 class="text-capitalize breadcrumb-title">Settings</h4>
          <div class="breadcrumb-action justify-content-center flex-wrap">
            <nav aria-label="breadcrumb">
              <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="#"><i class="uil uil-estate"></i>Home</a></li>
                <li class="breadcrumb-item active" aria-current="page">Settings</li>
              </ol>
            </nav>
          </div>
        </div>
      </div>
    </div>
    
    <div class="row">
      <div class="col-lg-6">
        <div class="card">
          <div class="card-header">
            <h5>General Settings</h5>
          </div>
          <div class="card-body">
            <form>
              <div class="form-group mb-3">
                <label for="siteName">Site Name</label>
                <input type="text" class="form-control" id="siteName" placeholder="Enter site name">
              </div>
              <div class="form-group mb-3">
                <label for="siteDescription">Site Description</label>
                <textarea class="form-control" id="siteDescription" rows="3"></textarea>
              </div>
              <div class="form-check form-switch mb-3">
                <input class="form-check-input" type="checkbox" id="maintenanceMode">
                <label class="form-check-label" for="maintenanceMode">Maintenance Mode</label>
              </div>
              <button type="submit" class="btn btn-primary">Save Changes</button>
            </form>
          </div>
        </div>
      </div>
      
      <div class="col-lg-6">
        <div class="card">
          <div class="card-header">
            <h5>User Preferences</h5>
          </div>
          <div class="card-body">
            <form>
              <div class="form-group mb-3">
                <label for="language">Language</label>
                <select class="form-select" id="language">
                  <option value="en">English</option>
                  <option value="fr">French</option>
                  <option value="es">Spanish</option>
                  <option value="de">German</option>
                </select>
              </div>
              <div class="form-group mb-3">
                <label for="timezone">Timezone</label>
                <select class="form-select" id="timezone">
                  <option value="utc">UTC</option>
                  <option value="est">Eastern Standard Time</option>
                  <option value="cst">Central Standard Time</option>
                  <option value="pst">Pacific Standard Time</option>
                </select>
              </div>
              <div class="form-check form-switch mb-3">
                <input class="form-check-input" type="checkbox" id="darkMode">
                <label class="form-check-label" for="darkMode">Dark Mode</label>
              </div>
              <button type="submit" class="btn btn-primary">Save Preferences</button>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
