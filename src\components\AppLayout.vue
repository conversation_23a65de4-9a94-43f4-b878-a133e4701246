<script setup>
import AppHeader from './AppHeader.vue'
import AppOverlay from './AppOverlay.vue'
import AppSidebar from './AppSidebar.vue'
</script>

<template>
  <div class="mobile-search">
    <form action="/" class="search-form">
      <img src="/img/svg/search.svg" alt="search" class="svg">
      <input class="form-control me-sm-2 box-shadow-none" type="search" placeholder="Search..." aria-label="Search">
    </form>
  </div>
  <div class="mobile-author-actions"></div>

  <AppHeader>
    <template #top-menu>
      <slot name="top-menu"></slot>
    </template>
    <template #navbar-right>
      <slot name="navbar-right"></slot>
    </template>
  </AppHeader>

  <AppSidebar>
    <slot name="sidebar"></slot>
  </AppSidebar>

  <div class="contents">
    <div class="container-fluid">
      <slot></slot>
    </div>
  </div>

  <footer class="footer-wrapper">
    <div class="container-fluid">
      <div class="row">
        <div class="col-md-6">
          <div class="footer-copyright">
            <p>© 2025 <a href="#">HCRM</a></p>
          </div>
        </div>
        <div class="col-md-6">
          <div class="footer-menu text-end">
            <ul>
              <li><a href="#">About</a></li>
              <li><a href="#">Team</a></li>
              <li><a href="#">Contact</a></li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </footer>

  <AppOverlay />
</template>
