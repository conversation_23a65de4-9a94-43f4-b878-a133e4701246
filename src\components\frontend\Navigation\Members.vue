<template>
  <div class="committee-container">
    <!-- Error State -->
    <div v-if="error" class="error-state">
      <div class="error-content">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="48"
          height="48"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <circle cx="12" cy="12" r="10"></circle>
          <line x1="12" y1="8" x2="12" y2="12"></line>
          <line x1="12" y1="16" x2="12.01" y2="16"></line>
        </svg>
        <h3>Unable to Load Data</h3>
        <p>{{ error }}</p>
      </div>
    </div>

    <!-- Rest of the template -->
    <template v-else>
      <!-- Tabs Navigation -->
      <div v-if="tabs.length > 0" class="tabs-navigation">
        <button
          v-for="tab in tabs"
          :key="tab.id"
          :class="['tab-button', { active: activeTab === tab.name }]"
          @click="activeTab = tab.name"
        >
          {{ tab.name }}
        </button>
      </div>

      <!-- Loading State -->
      <div v-if="isLoading" class="loading-state">
        <p>Loading members data...</p>
      </div>

      <!-- No Data State -->
      <div v-else-if="tabs.length === 0" class="no-data-state">
        <div class="no-data-content">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="64"
            height="64"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
            <circle cx="9" cy="7" r="4"></circle>
            <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
            <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
          </svg>
          <h3>No Members Data Found</h3>
          <p>There are currently no committee members to display.</p>
        </div>
      </div>

      <!-- Members Grid -->
      <div v-else>
        <!-- No Members in Selected Tab -->
        <div v-if="filteredMembers.length === 0" class="no-members-state">
          <div class="no-data-content">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="48"
              height="48"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <circle cx="12" cy="12" r="10"></circle>
              <line x1="12" y1="8" x2="12" y2="12"></line>
              <line x1="12" y1="16" x2="12.01" y2="16"></line>
            </svg>
            <h3>No Members Found</h3>
            <p>There are no members in the selected committee.</p>
          </div>
        </div>

        <!-- Members Grid -->
        <div v-else class="members-grid">
          <div v-for="member in filteredMembers" :key="member.id" class="member-card">
            <div class="member-image">
              <img
                :src="member.photoUrl || '/img/avatar-placeholder.jpg'"
                :alt="member.name"
                @error="(e) => (e.target.src = '/img/avatar-placeholder.jpg')"
              />
            </div>
            <div class="member-info">
              <h3 class="member-name">{{ member.name }}</h3>
              <p class="member-designation">{{ member.staffDesignation }}</p>
              <p v-if="member.staffContactNumber1" class="member-contact">
                <i class="fas fa-phone-alt"></i> {{ member.staffContactNumber1 }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup>
import {
  computed,
  onMounted,
  ref,
} from 'vue'

import { useMemberStore } from '@/stores/memberStore'

// State
const committeeData = ref([]);
const activeTab = ref("");
const tabs = ref([]);
const isLoading = ref(false);
const error = ref(null);

// Mock data

const memberStore = useMemberStore();
const mockData = ref(null);
onMounted(async () => {
  mockData.value = await memberStore.fetchFullCommittees();
  initializeData();

  console.log(mockData.value);
});

// Initialize data from mock or API
const initializeData = () => {
  isLoading.value = true;
  error.value = null;

  try {
    // Use .value for refs
    if (!mockData.value) {
      throw new Error("No data available");
    }

    if (mockData.value.status !== 1) {
      throw new Error("Invalid data status");
    }
    if (
      !mockData.value.committee ||
      !Array.isArray(mockData.value.committee) ||
      mockData.value.committee.length === 0
    ) {
      throw new Error("No committee data available");
    }
    // Process valid data
    committeeData.value = mockData.value.committee.map((committee) => ({
      ...committee,
      subHeaders: Array.isArray(committee.subHeaders) ? committee.subHeaders : [],
    }));

    tabs.value = committeeData.value.map((committee) => ({
      id: committee.id || Math.random().toString(36).substr(2, 9),
      name: committee.name || "Unnamed Committee",
    }));

    if (tabs.value.length > 0) {
      activeTab.value = tabs.value[0].name;
    }
  } catch (err) {
    console.error("Error initializing data:", err);
    error.value = err.message || "Failed to load committee data";
    committeeData.value = [];
    tabs.value = [];
    activeTab.value = "";
  } finally {
    isLoading.value = false;
  }
};

// Filter members based on active tab
const filteredMembers = computed(() => {
  if (!activeTab.value || committeeData.value.length === 0) {
    return [];
  }

  const activeCommittee = committeeData.value.find(
    (committee) => committee.name === activeTab.value
  );

  if (!activeCommittee || !Array.isArray(activeCommittee.subHeaders)) {
    return [];
  }

  // Map members to ensure all required properties have fallbacks
  return activeCommittee.subHeaders.map((member) => ({
    id: member.id || Math.random().toString(36).substr(2, 9),
    name: member.name || "Unknown Member",
    photoUrl: member.photoUrl || null,
    staffDesignation: member.staffDesignation || "No Designation",
    staffContactNumber1: member.staffContactNumber1 || null,
    // Include other properties as needed
  }));
});


</script>

<style scoped>
.committee-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px 0;
}

.tabs-navigation {
  display: flex;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 30px;
  overflow-x: auto;
}

.tab-button {
  padding: 12px 20px;
  background: none;
  border: none;
  font-size: 16px;
  font-weight: 500;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.tab-button.active {
  color: #3b82f6;
  border-bottom: 2px solid #3b82f6;
}

.members-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 24px;
}

.member-card {
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: transform 0.3s ease;
}

.member-card:hover {
  transform: translateY(-5px);
}

.member-image {
  width: 100%;
  height: 220px;
  overflow: hidden;
}

.member-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.member-info {
  padding: 16px;
  text-align: center;
}

.member-name {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.member-designation {
  font-size: 14px;
  color: #4b5563;
  margin-bottom: 8px;
}

.member-contact {
  font-size: 14px;
  color: #6b7280;
}

@media (max-width: 768px) {
  .members-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  }
}

.loading-state,
.no-data-state,
.no-members-state,
.error-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  width: 100%;
}

.no-data-content,
.error-content {
  text-align: center;
  padding: 30px;
  color: #6b7280;
}

.no-data-content svg,
.error-content svg {
  margin-bottom: 16px;
  color: #9ca3af;
}

.error-content svg {
  color: #ef4444;
}

.no-data-content h3,
.error-content h3 {
  font-size: 18px;
  margin-bottom: 8px;
  color: #4b5563;
}

.error-content h3 {
  color: #b91c1c;
}

.no-data-content p,
.error-content p {
  font-size: 14px;
}
</style>
