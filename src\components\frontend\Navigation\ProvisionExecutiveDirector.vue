<template>
  <section class="provisions-section py-10 bg-gradient-to-br from-blue-50 to-blue-100">
    <div class="container mx-auto px-4" v-if="executiveDirector">
      <div class="max-w-6xl mx-auto">
        <!-- Header -->
        <div class="text-center mb-8">
          <h2
            class="text-2xl md:text-3xl font-bold text-blue-600 mb-2 relative inline-block"
          >
            Provisions relating to Executive Director
            <span
              class="absolute bottom-0 left-0 w-full h-0.5 bg-gradient-to-r from-blue-400 to-blue-200 -mb-1"
            ></span>
          </h2>
        </div>

        <!-- Main Content -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <!-- Left Column - Provisions -->
          <div class="lg:col-span-2">
            <div
              class="bg-white rounded-xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl"
            >
              <div
                class="bg-gradient-to-r from-blue-500 to-blue-400 text-white py-3 px-5 flex items-center"
              >
                <div
                  class="p-1.5 bg-white bg-opacity-20 backdrop-filter backdrop-blur-sm rounded-full mr-3 flex-shrink-0"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-4 w-4"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                    />
                  </svg>
                </div>
                <h3 class="text-base font-semibold">Executive Director Provisions</h3>
              </div>

              <div class="p-5">
                <!-- Dynamic Executive Director Sections -->
                <div class="">
                  <div
                    v-for="(item, index) in executiveDirector.aboutUsDetails"
                    :key="index"
                    class="mb-6"
                  >
                    <h4
                      class="text-blue-600 font-medium mb-2 border-b border-gray-100 pb-1"
                      v-html="item.header"
                    ></h4>

                    <!-- Render rich text HTML -->
                    <div class="rich-text" v-html="item.message"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Right Column - Executive Director Profiles -->
          <RightExecutiveDirector />
        </div>
      </div>
    </div>
    <div v-else></div>
  </section>

  <!-- Offences and Punishments Section -->
  <section class="provisions-section py-8 bg-gradient-to-br from-blue-50 to-blue-100">
    <div class="container mx-auto px-4">
      <div class="max-w-6xl mx-auto">
        <div
          class="bg-white rounded-xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl"
        >
          <div
            class="bg-gradient-to-r from-blue-500 to-blue-400 text-white py-3 px-5 flex items-center"
          >
            <div
              class="p-1.5 bg-white bg-opacity-20 backdrop-filter backdrop-blur-sm rounded-full mr-3 flex-shrink-0"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16H9m3 0h3"
                />
              </svg>
            </div>
            <h3 class="text-base font-semibold">Offences and Punishments</h3>
          </div>

          <div class="p-4">
            <div
              class="rich-text  mb-4"
              v-if="offence"
              v-html="offence.message"
            >
            </div>
            <div v-else>
              <div class="text-center text-gray-500 py-8 text-lg font-medium">No Data Found</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
  <FormComponent />
</template>

<script setup>
import {
  onMounted,
  ref,
} from 'vue'

import api from '@/api/api'
import { ORG_ID } from '@/api/endpoint'
import RightExecutiveDirector from '@/components/RightExecutiveDirector.vue'
import { useMemberStore } from '@/stores/memberStore'

const executiveDirector = ref(null);
const offence = ref(null);

//API for The mockExecutive
async function fetchMockExecutive() {
  try {
    const functionalData = await api.get(
      `/customer/webComponent/${ORG_ID}/executive-director-provisions`
    );
    executiveDirector.value = functionalData.data;
    console.log(executiveDirector.value);
  } catch (error) {
    console.error("Error fetching about us data:", error);
  }
}

async function fetchOffence() {
  try {
    const functionalData = await api.get(
      `/customer/webComponent/${ORG_ID}/offences-punishments`
    );
    console.log(functionalData);
    offence.value = functionalData.data.aboutUsDetails[0];
  } catch (error) {
    console.error("Error fetching about us data:", error);
  }
}

onMounted(() => {
  fetchMockExecutive();
  fetchOffence();
  // offence.value = mockOffence.aboutUsDetails[0];
});
</script>

<style scoped>
.provisions-section {
  position: relative;
  overflow: hidden;
}

.provisions-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%232563eb' fill-opacity='0.03' fill-rule='evenodd'/%3E%3C/svg%3E");
  opacity: 0.5;
  z-index: 0;
}

.provisions-card {
  position: relative;
  transition: all 0.3s ease;
}

.provisions-card:hover {
  transform: translateY(-5px);
}

/* Animation for the profile images */
.rounded-full {
  transition: all 0.3s ease;
}

.rounded-full:hover {
  transform: scale(1.05);
  border-color: #3b82f6;
}

/* Rich text content styles */
::v-deep(.rich-text ul) {
  list-style-type: disc;
  padding-left: 1.5rem;
  margin-bottom: 1rem;
}

::v-deep(.rich-text ol) {
  list-style-type: decimal;
  padding-left: 1.5rem;
  margin-bottom: 1rem;
}

::v-deep(.rich-text li) {
  margin-bottom: 0.25rem;
  line-height: 1.6;
}

::v-deep(.rich-text h1) {
  font-size: 2rem;
  font-weight: 700;
  margin-top: 1.5rem;
  margin-bottom: 1rem;
}

::v-deep(.rich-text h2) {
  font-size: 1.5rem;
  font-weight: 700;
  margin-top: 1.25rem;
  margin-bottom: 0.75rem;
}

::v-deep(.rich-text h3) {
  font-size: 1.25rem;
  font-weight: 600;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
}

::v-deep(.rich-text h4) {
  font-size: 1.125rem;
  font-weight: 600;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
}

::v-deep(.rich-text h5) {
  font-size: 1rem;
  font-weight: 600;
  margin-top: 0.75rem;
  margin-bottom: 0.5rem;
}

::v-deep(.rich-text h6) {
  font-size: 0.875rem;
  font-weight: 600;
  margin-top: 0.5rem;
  margin-bottom: 0.25rem;
}

::v-deep(.rich-text p) {
  margin-bottom: 0.5rem;
  line-height: 1.7;
}

::v-deep(.rich-text a) {
  color: #2563eb;
  text-decoration: underline;
  transition: color 0.2s;
}

::v-deep(.rich-text a:hover) {
  color: #1d4ed8;
}

::v-deep(.rich-text blockquote) {
  border-left: 4px solid #2563eb;
  background: #f1f5f9;
  padding: 0.75rem 1rem;
  margin: 1rem 0;
  font-style: italic;
  color: #334155;
}

::v-deep(.rich-text img) {
  max-width: 100%;
  height: auto;
  margin: 1rem 0;
  border-radius: 0.5rem;
}

::v-deep(.rich-text strong) {
  font-weight: 700;
}

::v-deep(.rich-text em) {
  font-style: italic;
}

::v-deep(.rich-text code) {
  background: #f3f4f6;
  color: #2563eb;
  padding: 0.2em 0.4em;
  border-radius: 0.25em;
  font-size: 0.95em;
  font-family: 'Fira Mono', 'Consolas', monospace;
}

::v-deep(.rich-text pre) {
  background: #f3f4f6;
  color: #334155;
  padding: 1em;
  border-radius: 0.5em;
  overflow-x: auto;
  margin: 1em 0;
  font-family: 'Fira Mono', 'Consolas', monospace;
  font-size: 0.95em;
}

::v-deep(.rich-text table) {
  width: 100%;
  border-collapse: collapse;
  margin: 1em 0;
}

::v-deep(.rich-text th),
::v-deep(.rich-text td) {
  border: 1px solid #e5e7eb;
  padding: 0.5em 1em;
  text-align: left;
}

::v-deep(.rich-text th) {
  background: #f1f5f9;
  font-weight: 600;
}

::v-deep(.rich-text hr) {
  border: none;
  border-top: 1px solid #e5e7eb;
  margin: 1.5em 0;
}

::v-deep(.rich-text ul ul),
::v-deep(.rich-text ol ol),
::v-deep(.rich-text ul ol),
::v-deep(.rich-text ol ul) {
  margin-bottom: 0;
  margin-top: 0.25rem;
}


</style>
