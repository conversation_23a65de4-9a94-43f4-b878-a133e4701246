<template>
  <div v-if="isVisible" class="banner-ad-overlay" @click="closeBanner">
    <div class="banner-ad-container" @click.stop>
      <div class="banner-ad-content">
        <button class="close-btn" @click="closeBanner">&times;</button>
        <h2 class="banner-title">{{ bannerData.title }}</h2>
        <div class="banner-body" v-html="bannerData.content"></div>
        <div class="banner-image" v-if="bannerData.image">
          <img :src="bannerData.image" :alt="bannerData.title">
        </div>
        <button class="banner-action-btn" v-if="bannerData.actionText" @click="handleAction">
          {{ bannerData.actionText }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import {
  onMounted,
  ref,
} from 'vue'

// Static banner data
const bannerData = {
  title: "Important Notice",
  content: "Water supply will be interrupted in Ward 5 on July 15th from 10:00 AM to 2:00 PM due to maintenance work.",
  image: "https://watersoft.com.np/WaterTariffSystem-web/appImageUpload/IMAGE_BANNER_89_1736939845888.jpg",
  actionText: "Learn More",
  actionUrl: "/notices/vacancy"
};

const isVisible = ref(false);

const closeBanner = () => {
  isVisible.value = false;
  sessionStorage.setItem('bannerClosed', 'true');
};

const handleAction = () => {
  window.location.href = bannerData.actionUrl;
};

onMounted(() => {
  const wasClosed = sessionStorage.getItem('bannerClosed') === 'true';
  
  if (!wasClosed) {
    setTimeout(() => {
      isVisible.value = true;
    }, 1000);
  }
});
</script>

<style scoped>
.banner-ad-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.banner-ad-container {
  background-color: white;
  border-radius: 8px;
  max-width: 600px;
  width: 90%;
  position: relative;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.banner-ad-content {
  padding: 30px;
}

.close-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
}

.banner-title {
  font-size: 1.5rem;
  margin-bottom: 15px;
  color: #1e3a8a;
}

.banner-body {
  margin-bottom: 20px;
  line-height: 1.5;
}

.banner-image {
  margin-bottom: 20px;
}

.banner-image img {
  max-width: 100%;
  border-radius: 4px;
}

.banner-action-btn {
  background-color: #1e3a8a;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
}

.banner-action-btn:hover {
  background-color: #1e40af;
}
</style>