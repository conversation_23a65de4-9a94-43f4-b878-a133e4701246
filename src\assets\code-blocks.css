/* Code block styling */
.code-block {
  font-family: 'Fira Code', 'Consolas', 'Monaco', 'Andale Mono', 'Ubuntu Mono', monospace;
  line-height: 1.5;
  position: relative;
  overflow: auto;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.code-block code {
  display: block;
  padding: 1rem;
  color: #333;
  background: #f8f9fa;
  border-radius: 6px;
  overflow-x: auto;
}

/* Language specific styling */
.language-javascript {
  color: #333;
}

.language-javascript .keyword {
  color: #0033b3;
  font-weight: bold;
}

.language-javascript .string {
  color: #067d17;
}

.language-javascript .comment {
  color: #8c8c8c;
  font-style: italic;
}

.language-javascript .function {
  color: #6f42c1;
}

.language-html {
  color: #333;
}

.language-html .tag {
  color: #22863a;
}

.language-html .attr-name {
  color: #6f42c1;
}

.language-html .attr-value {
  color: #032f62;
}

/* Line numbers */
.code-block.with-line-numbers {
  padding-left: 3.5rem;
}

.code-block.with-line-numbers::before {
  content: attr(data-line-numbers);
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3rem;
  padding: 1rem 0.5rem;
  text-align: right;
  color: #999;
  background-color: #f1f1f1;
  border-right: 1px solid #ddd;
  user-select: none;
}

/* Copy button */
.code-block .copy-button {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  padding: 0.25rem 0.5rem;
  background-color: #f8f9fa;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.75rem;
  color: #666;
  cursor: pointer;
  opacity: 0;
  transition: opacity 0.2s;
}

.code-block:hover .copy-button {
  opacity: 1;
}

.code-block .copy-button:hover {
  background-color: #e9ecef;
  color: #333;
}

.code-block .copy-button:active {
  background-color: #dee2e6;
}

/* Syntax highlighting */
.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
  color: #8c8c8c;
  font-style: italic;
}

.token.punctuation {
  color: #999;
}

.token.property,
.token.tag,
.token.boolean,
.token.number,
.token.constant,
.token.symbol {
  color: #905;
}

.token.selector,
.token.attr-name,
.token.string,
.token.char,
.token.builtin {
  color: #690;
}

.token.operator,
.token.entity,
.token.url,
.language-css .token.string,
.style .token.string {
  color: #9a6e3a;
}

.token.atrule,
.token.attr-value,
.token.keyword {
  color: #07a;
}

.token.function,
.token.class-name {
  color: #dd4a68;
}

.token.regex,
.token.important,
.token.variable {
  color: #e90;
}

.token.important,
.token.bold {
  font-weight: bold;
}

.token.italic {
  font-style: italic;
}

.token.entity {
  cursor: help;
}
