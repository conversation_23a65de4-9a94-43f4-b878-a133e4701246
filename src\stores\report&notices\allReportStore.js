import { defineStore } from 'pinia'

import api from '@/api/api'
import { ORG_ID } from '@/api/endpoint'

// Mock data for initial state
const mockData = {
    annual: [],
    audit: [],
    waterQualityTestReport: [],
    tender: [],
    tariff: [],
    downloads: [],
    consumer: [],
    career: []
}

export const useAllReportStore = defineStore('allReport', {
    state: () => ({
        reports: mockData,
        loading: false,
        error: null
    }),
    getters: {
        getAllDocs: (state) =>
            Object.values(state.reports).flatMap(reportArr =>
                reportArr.flatMap(r => r.aboutUsDocs)
            )
    },
    actions: {
        async fetchAnnualReports() {
            this.loading = true;
            this.error = null;
            try {
                const response = await api.get(
                    `/customer/webComponent/${ORG_ID}/annual-reports`
                );
                this.reports.annual = response.data.aboutUsDetails;
            } catch (err) {
                console.log(err);
                this.error = err;
            } finally {
                this.loading = false;
            }
        },
        async fetchAuditReports() {
            this.loading = true;
            this.error = null;
            try {
                const response = await api.get(
                    `/customer/webComponent/${ORG_ID}/audit-reports`
                );
                this.reports.audit = response.data.aboutUsDetails;
            } catch (err) {
                this.error = err;
            } finally {
                this.loading = false;
            }
        },
        async fetchWaterQualityTestReports() {
            this.loading = true;
            this.error = null;
            try {
                const response = await api.get(
                    `/customer/webComponent/${ORG_ID}/water-quality-test-report`
                );
                this.reports.waterQualityTestReport = response.data.aboutUsDetails;
            } catch (err) {
                this.error = err;
            } finally {
                this.loading = false;
            }
        },
        async fetchDownloads() {
            this.loading = true;
            this.error = null;
            try {
                const response = await api.get(
                    `/customer/webComponent/${ORG_ID}/downloads`
                );
                this.reports.downloads = response.data.aboutUsDetails;
            } catch (err) {
                this.error = err;
            } finally {
                this.loading = false;
            }
        },
        async fetchTenderReports() {
            this.loading = true;
            this.error = null;
            try {
                const response = await api.get(
                    `/customer/webComponent/${ORG_ID}/tender-notices`
                );
                this.reports.tender = response.data.aboutUsDetails;
            } catch (err) {
                this.error = err;
            } finally {
                this.loading = false;
            }
        },
        async fetchTariffReports() {
            this.loading = true;
            this.error = null;
            try {
                const response = await api.get(
                    `/customer/webComponent/${ORG_ID}/tariff-and-others`
                );
                this.reports.tariff = response.data.aboutUsDetails;
            } catch (err) {
                this.error = err;
            } finally {
                this.loading = false;
            }
        },
        async fetchConsumerReports() {
            this.loading = true;
            this.error = null;
            try {
                const response = await api.get(
                    `/customer/webComponent/${ORG_ID}/consumer-notice`
                );
                this.reports.consumer = response.data.aboutUsDetails;
            } catch (err) {
                this.error = err;
            } finally {
                this.loading = false;
            }
        },
        async fetchCareerReports() {
            this.loading = true;
            this.error = null;
            try {
                const response = await api.get(
                    `/customer/webComponent/${ORG_ID}/career`
                );
                this.reports.career = response.data.aboutUsDetails;
            } catch (err) {
                this.error = err;
            } finally {
                this.loading = false;
            }
        },

    }
});