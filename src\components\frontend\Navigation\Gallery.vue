<script setup>
import {
  onMounted,
  ref,
} from 'vue'

import api from '@/api/api'
import { ORG_ID } from '@/api/endpoint'

const DEFAULT_IMAGE = "https://via.placeholder.com/300x200?text=Image+Not+Found";

const gallerySections = ref([]);
const isLoading = ref(false);

// Modal state
const showModal = ref(false);
const modalImages = ref([]);
const modalIndex = ref(0);
const modalHeader = ref("");

const openModal = (section) => {
  if (section.galleryUrls && section.galleryUrls.length) {
    modalImages.value = section.galleryUrls;
    modalIndex.value = 0; // Start with first image
    modalHeader.value = section.header;
    showModal.value = true;
  }
};

const closeModal = () => {
  showModal.value = false;
};

const prevImage = () => {
  if (modalIndex.value > 0) {
    modalIndex.value--;
  }
};

const nextImage = () => {
  if (modalIndex.value < modalImages.value.length - 1) {
    modalIndex.value++;
  }
};

const loadGallery = async () => {
  isLoading.value = true;
  try {
    const response = await api.get(`/customer/organizationGallery/web/${ORG_ID}`);
    gallerySections.value = response.data.messageWithImg || [];
  } catch (error) {
    console.error("Failed to load gallery:", error);
    gallerySections.value = [];
  } finally {
    isLoading.value = false;
  }
};

onMounted(() => {
  loadGallery();
});
</script>

<template>
  <section class="gallery-section py-8 bg-gradient-to-br from-blue-50 to-blue-100">
    <div class="container mx-auto px-4">
      <div class="max-w-6xl mx-auto">
        <div class="text-center mb-8">
          <h2
            class="text-2xl md:text-3xl font-bold text-blue-600 mb-2 relative inline-block"
          >
            Photo Gallery
            <span
              class="absolute bottom-0 left-0 w-full h-0.5 bg-gradient-to-r from-blue-400 to-blue-200 -mb-1"
            ></span>
          </h2>
          <p class="text-gray-600 mt-2">Explore our collection of images</p>
        </div>

        <div v-if="isLoading" class="text-center py-10 text-blue-600 font-semibold">
          Loading gallery...
        </div>

        <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <!-- Single card for each section showing only the thumbnail (imageUrl) -->
          <div
            v-for="section in gallerySections"
            :key="section.id"
            class="bg-white rounded-lg shadow-md overflow-hidden cursor-pointer transition-transform duration-200 hover:scale-[1.02]"
            @click="openModal(section)"
          >
            <!-- Thumbnail Image -->
            <div class="h-48 overflow-hidden">
              <img
                :src="
                  section.imageUrl && section.imageUrl !== 'n/a'
                    ? section.imageUrl
                    : DEFAULT_IMAGE
                "
                :alt="section.header"
                class="w-full h-full object-cover"
              />
            </div>

            <!-- Section Info -->
            <div class="p-4">
              <h3 class="text-lg font-bold text-blue-700 mb-1">{{ section.header }}</h3>
              <div class="text-gray-600 text-sm" v-html="section.message"></div>
              <div class="mt-2 text-xs text-gray-500">
                {{ section.galleryUrls?.length || 0 }} photos available
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal for gallery images -->
    <div
      v-if="showModal"
      class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-80 px-4"
      @click.self="closeModal"
    >
      <div class="relative bg-white rounded-xl shadow-xl max-w-3xl w-full p-6">
        <!-- Close Button -->
        <button
          class="absolute top-4 right-4 text-gray-400 hover:text-gray-700 text-2xl"
          @click="closeModal"
        >
          &times;
        </button>

        <!-- Modal Header -->
        <h3 class="text-xl font-semibold text-center text-blue-700 mb-4">
          {{ modalHeader }}
        </h3>

        <!-- Image Viewer -->
        <div class="relative flex items-center justify-center">
          <!-- Left Arrow -->
          <button
            class="absolute left-0 ml-2 bg-white bg-opacity-80 hover:bg-opacity-100 p-2 rounded-full shadow-lg text-blue-600 hover:text-blue-800 disabled:opacity-50"
            @click="prevImage"
            :disabled="modalIndex === 0"
          >
            <svg
              class="w-6 h-6"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              viewBox="0 0 24 24"
            >
              <path stroke-linecap="round" stroke-linejoin="round" d="M15 19l-7-7 7-7" />
            </svg>
          </button>

          <!-- Main Image -->
          <img
            :src="modalImages[modalIndex] || DEFAULT_IMAGE"
            class="rounded-lg shadow object-contain w-full max-h-[70vh] transition-transform duration-200"
            :alt="modalHeader + ' image ' + (modalIndex + 1)"
          />

          <!-- Right Arrow -->
          <button
            class="absolute right-0 mr-2 bg-white bg-opacity-80 hover:bg-opacity-100 p-2 rounded-full shadow-lg text-blue-600 hover:text-blue-800 disabled:opacity-50"
            @click="nextImage"
            :disabled="modalIndex === modalImages.length - 1"
          >
            <svg
              class="w-6 h-6"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              viewBox="0 0 24 24"
            >
              <path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>

        <!-- Counter -->
        <div class="text-center mt-3 text-sm text-gray-500">
          {{ modalIndex + 1 }} / {{ modalImages.length }}
        </div>
      </div>
    </div>
  </section>
</template>
