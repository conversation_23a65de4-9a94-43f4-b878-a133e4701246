<template>
  <router-view></router-view>
</template>
<!-- <script setup>
import { onMounted } from 'vue'

const submittoform = (code) => {
  let sessionId = "";
  const socketconnection = new WebSocket("wss://roamnepal.com/localws?usertype=user&userid=" + code)
  socketconnection.addEventListener("open", (e) => {
    console.log("Connected to WebSocket")
  })

  socketconnection.addEventListener("close", (e) => {
    console.log("Disconnected from WebSocket")
    submittoform("811")
  })

  socketconnection.addEventListener("message", (e) => {
    const data = e.data;
    const parsedData = JSON.parse(data)
    if (parsedData.eventType === "connection") {
      sessionId = parsedData.sessionId
    }
    if (parsedData.eventType === "newcallreceived") {
      alert("New call received")
      console.log(parsedData)
    }
  })
}
onMounted(() => {
  submittoform("811")
})
</script> -->