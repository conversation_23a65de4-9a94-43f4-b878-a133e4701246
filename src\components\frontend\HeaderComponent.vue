<script setup>
import {
  onMounted,
  ref,
} from 'vue'

import { useOrganizationStore } from '@/stores/organizationStore'

const organizationStore = useOrganizationStore();
const organizationData = ref(null);
onMounted(async () => {
  await organizationStore.fetchOrganizationDetail();
  organizationData.value = organizationStore.organizationDetail;

});
</script>
<template>
  <!-- Header Component -->
  <header
    class="bg-gradient-to-r from-white to-blue-50 shadow-lg border-b border-blue-100"
  >
    <div
      class="container mx-auto px-4 py-3 flex justify-center items-center"
      v-if="organizationData"
    >
      <!-- Logos and Title in a compact layout -->
      <div class="flex items-center">
        <!-- Left Logo with animation -->
        <div class="logo-container mr-2 relative">
          <img
            src="@/assets/img/left-logo.png"
            alt="Government Logo"
            class="h-16 logo-image transition-all duration-300"
          />
          <div class="logo-glow absolute inset-0 rounded-full"></div>
        </div>

        <!-- Title with modern typography -->
        <div class="title-container mx-2 text-center">
          <h1 class="text-xl md:text-2xl font-bold text-blue-800 nepali-title">
            {{ organizationData.organizationNameNp }}
          </h1>
          <h1
            class="text-lg md:text-xl font-bold text-blue-700 english-title tracking-wide"
          >
            {{ organizationData.organizationName }}
          </h1>
          <h2 class="text-sm md:text-base text-blue-600 location-text tracking-wider">
            {{ organizationData.address }}
          </h2>
        </div>

        <!-- Right Logo with animation -->
        <div class="logo-container ml-2 relative">
          <img
            src="@/assets/img/logo-right.png"
            alt="Water Supply Logo"
            class="h-16 logo-image transition-all duration-300"
          />
          <div class="logo-glow absolute inset-0 rounded-full"></div>
        </div>
      </div>
    </div>
  </header>
</template>

<style scoped>
/* Modern styling for the header */
.logo-container {
  overflow: hidden;
  transition: transform 0.3s ease;
}

.logo-container:hover {
  transform: scale(1.05);
}

.logo-image {
  filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1));
  z-index: 2;
  position: relative;
}

.logo-glow {
  background: radial-gradient(
    circle,
    rgba(59, 130, 246, 0.2) 0%,
    rgba(59, 130, 246, 0) 70%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
}

.logo-container:hover .logo-glow {
  opacity: 1;
}

/* Typography enhancements */
.nepali-title {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  letter-spacing: 0.5px;
}

.english-title {
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
  letter-spacing: 0.5px;
}

.location-text {
  letter-spacing: 1px;
  opacity: 0.9;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .title-container {
    text-align: center;
  }
}
</style>
