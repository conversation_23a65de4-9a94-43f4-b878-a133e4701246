/* Vue Validate Styles */

/* Input states */
.validate-input {
  position: relative;
  margin-bottom: 1.5rem;
}

.validate-input input,
.validate-input select,
.validate-input textarea {
  transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.validate-input.is-invalid input,
.validate-input.is-invalid select,
.validate-input.is-invalid textarea {
  border-color: var(--color-danger, #ff4d4f) !important;
  box-shadow: 0 0 0 1px var(--color-danger, #ff4d4f) !important;
}

.validate-input.is-valid input,
.validate-input.is-valid select,
.validate-input.is-valid textarea {
  border-color: var(--color-success, #52c41a) !important;
  box-shadow: 0 0 0 1px var(--color-success, #52c41a) !important;
}

/* Error message */
.validation-error {
  display: none;
  color: var(--color-danger, #ff4d4f);
  font-size: 0.875rem;
  margin-top: 0.25rem;
  position: absolute;
  bottom: -1.25rem;
  left: 0;
  width: 100%;
}

.validate-input.is-invalid .validation-error {
  display: block;
}

/* Form group styling */
.v-form-group {
  margin-bottom: 1.5rem;
  position: relative;
}

.v-form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.v-form-group input,
.v-form-group select,
.v-form-group textarea {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--border-color, #d9d9d9);
  border-radius: 4px;
  transition: all 0.3s;
}

.v-form-group input:focus,
.v-form-group select:focus,
.v-form-group textarea:focus {
  outline: none;
  border-color: var(--color-primary, #1890ff);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.v-form-group.has-error input,
.v-form-group.has-error select,
.v-form-group.has-error textarea {
  border-color: var(--color-danger, #ff4d4f);
  animation: shake 0.6s ease-in-out;
}

.v-form-group.has-error .error-message {
  color: var(--color-danger, #ff4d4f);
  font-size: 0.875rem;
  margin-top: 0.25rem;
  animation: fadeIn 0.3s ease-in-out;
}

/* Checkbox and radio styling */
.v-form-check {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
  position: relative;
}

.v-form-check input[type="checkbox"],
.v-form-check input[type="radio"] {
  margin-right: 0.5rem;
  cursor: pointer;
}

.v-form-check label {
  cursor: pointer;
  user-select: none;
}

.v-form-check.has-error input[type="checkbox"],
.v-form-check.has-error input[type="radio"] {
  outline: 1px solid var(--color-danger, #ff4d4f);
}

.v-form-check .error-message {
  position: absolute;
  left: 0;
  top: 100%;
  font-size: 0.75rem;
  color: var(--color-danger, #ff4d4f);
  animation: fadeIn 0.3s ease-in-out;
}

/* Form buttons */
.v-form-buttons {
  display: flex;
  gap: 0.5rem;
  margin-top: 1.5rem;
}

/* Loading state */
.v-form-loading {
  opacity: 0.7;
  pointer-events: none;
}

.v-form-loading::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.5);
  z-index: 1;
}

/* Animations */
@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
  20%, 40%, 60%, 80% { transform: translateX(5px); }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.validate-input.is-invalid input,
.validate-input.is-invalid select,
.validate-input.is-invalid textarea {
  animation: shake 0.6s ease-in-out;
}

/* Success animation */
@keyframes success-pulse {
  0% { box-shadow: 0 0 0 0 rgba(82, 196, 26, 0.4); }
  70% { box-shadow: 0 0 0 10px rgba(82, 196, 26, 0); }
  100% { box-shadow: 0 0 0 0 rgba(82, 196, 26, 0); }
}

.v-form-group input:valid:not(:placeholder-shown),
.v-form-group select:valid:not([value=""]),
.v-form-group textarea:valid:not(:placeholder-shown) {
  border-color: var(--color-success, #52c41a);
}
