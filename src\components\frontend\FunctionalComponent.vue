<script setup>
import {
  onMounted,
  ref,
} from 'vue'

import api from '@/api/api'
import { ORG_ID } from '@/api/endpoint'
import { useMemberStore } from '@/stores/memberStore'

const memberStore = useMemberStore();
const commiteeData = ref(null);
onMounted(async () => {
  await memberStore.fetchCommittees();
  commiteeData.value = memberStore.getCommitteeMemberImages("Board Members", 1)[0];
  console.log(commiteeData.value)
});



const response = ref(null);
// Function to fetch data from API (ready to uncomment later)
const fetchAboutUsData = async () => {
  try {
    const functionalData = await api.get(
      `/customer/webComponent/${ORG_ID}/functional-component`
    );
    response.value = functionalData.data.aboutUsDetails[0];
  } catch (error) {
    console.error("Error fetching about us data:", error);
  }
};

onMounted(() => {
  // API call implementation (commented for future use)
  fetchAboutUsData();

  // Add animations when component is mounted
  setTimeout(() => {
    const functionUnderline = document.querySelector(".function-underline");
    const functionListContainer = document.querySelector(".function-list-container");
    const directorCard = document.querySelector(".director-card");
    const functionListItems = document.querySelectorAll(".function-list li");

    if (functionUnderline) functionUnderline.classList.add("animate-expand");
    if (functionListContainer) functionListContainer.classList.add("animate-fade-in");
    if (directorCard) directorCard.classList.add("animate-slide-in");

    // Staggered animation for list items
    if (functionListItems.length) {
      functionListItems.forEach((item, index) => {
        setTimeout(() => {
          item.classList.add("animate-fade-in");
        }, 100 + index * 50);
      });
    }
  }, 300);
});
</script>

<template>
  <section class="functional-component py-16 bg-gradient-to-b from-blue-50 to-white">
    <div class="container mx-auto px-4">
      <!-- Section Header -->
      <div v-if="response" class="mb-12 text-center">
        <h2
          class="text-3xl md:text-4xl font-bold text-blue-900 mb-3 relative inline-block"
        >
          <!-- The main issue is it won't give me the data right now so lateron it will get populated so that we have to check the data ... -->
          {{ response.header }}
          <span
            class="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-24 h-1 bg-blue-600 rounded-full function-underline"
          ></span>
        </h2>
      </div>
      <div v-else class="text-center text-gray-500">Loading...</div>

      <!-- Content Grid -->
      <div class="flex flex-col lg:flex-row items-start gap-8 lg:gap-12 rich-text">
        <!-- Functions List -->
        <div
          v-if="response"
          v-html="response.message"
          class="w-full lg:w-2/3 bg-white rounded-xl shadow-lg p-6 md:p-8 function-list-container"
        ></div>
        <div
          v-else
          class="w-full lg:w-2/3 bg-white rounded-xl shadow-lg p-6 md:p-8 function-list-container"
        >
          <p>Loading...</p>
        </div>

        <!-- Director Card -->
        <div class="w-full lg:w-1/3 director-card">
          <div
            class="bg-white rounded-xl shadow-lg overflow-hidden transform transition-all duration-500 hover:shadow-xl"
          >
            <!-- Director Image -->
            <div class="relative overflow-hidden" v-if="commiteeData">
              <img
                :src="commiteeData.image"
                alt="Mr. Sanjay Baral"
                class="w-full h-auto object-cover transition-transform duration-700 hover:scale-105"
              />
              <div
                class="absolute inset-0 bg-gradient-to-t from-blue-900 to-transparent opacity-20"
              ></div>
            </div>

            <!-- Director Info -->
            <div class="p-6 text-center" v-if="commiteeData">
              <h3 class="text-xl font-bold text-blue-900 mb-1">{{commiteeData.name}}</h3>
              <p class="text-blue-700 font-medium mb-2">{{commiteeData.staffDesignation}}</p>
                <div class="flex items-center justify-center gap-2 mt-2">
                <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M3 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H5a2 2 0 01-2-2V5zm0 12a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H5a2 2 0 01-2-2v-2zm12-12a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zm0 12a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"/>
                </svg>
                <a
                  :href="`tel:${commiteeData.staffContactNumber1}`"
                  class="text-blue-700 font-semibold hover:underline transition"
                >
                  {{ commiteeData.staffContactNumber1 }}
                </a>
                </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<style scoped>
.functional-component {
  position: relative;
  overflow: hidden;
}

.functional-component::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%232563eb' fill-opacity='0.03' fill-rule='evenodd'/%3E%3C/svg%3E");
  opacity: 0.5;
  z-index: 0;
}

.function-list li {
  opacity: 0;
  transform: translateY(10px);
}

.animate-expand {
  animation: expand 0.8s ease forwards;
}

.animate-fade-in {
  animation: fadeIn 0.8s ease forwards;
}

.animate-slide-in {
  animation: slideIn 0.8s ease forwards;
}

@keyframes expand {
  from {
    width: 0;
  }
  to {
    width: 6rem;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .function-list li p {
    font-size: 0.95rem;
  }
}
/* Rich text content styles */
::v-deep(.rich-text ul) {
  list-style-type: disc;
  padding-left: 1.5rem;
  margin-bottom: 1rem;
}

::v-deep(.rich-text ol) {
  list-style-type: decimal;
  padding-left: 1.5rem;
  margin-bottom: 1rem;
}

::v-deep(.rich-text li) {
  margin-bottom: 0.25rem;
  line-height: 1.6;
}

::v-deep(.rich-text h1) {
  font-size: 2rem;
  font-weight: 700;
  margin-top: 1.5rem;
  margin-bottom: 1rem;
}

::v-deep(.rich-text h2) {
  font-size: 1.5rem;
  font-weight: 700;
  margin-top: 1.25rem;
  margin-bottom: 0.75rem;
}

::v-deep(.rich-text h3) {
  font-size: 1.25rem;
  font-weight: 600;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
}

::v-deep(.rich-text h4) {
  font-size: 1.125rem;
  font-weight: 600;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
}

::v-deep(.rich-text h5) {
  font-size: 1rem;
  font-weight: 600;
  margin-top: 0.75rem;
  margin-bottom: 0.5rem;
}

::v-deep(.rich-text h6) {
  font-size: 0.875rem;
  font-weight: 600;
  margin-top: 0.5rem;
  margin-bottom: 0.25rem;
}

::v-deep(.rich-text p) {
  margin-bottom: 0.5rem;
  line-height: 1.7;
}

::v-deep(.rich-text a) {
  color: #2563eb;
  text-decoration: underline;
  transition: color 0.2s;
}

::v-deep(.rich-text a:hover) {
  color: #1d4ed8;
}

::v-deep(.rich-text blockquote) {
  border-left: 4px solid #2563eb;
  background: #f1f5f9;
  padding: 0.75rem 1rem;
  margin: 1rem 0;
  font-style: italic;
  color: #334155;
}

::v-deep(.rich-text img) {
  max-width: 100%;
  height: auto;
  margin: 1rem 0;
  border-radius: 0.5rem;
}

::v-deep(.rich-text strong) {
  font-weight: 700;
}

::v-deep(.rich-text em) {
  font-style: italic;
}

::v-deep(.rich-text code) {
  background: #f3f4f6;
  color: #2563eb;
  padding: 0.2em 0.4em;
  border-radius: 0.25em;
  font-size: 0.95em;
  font-family: 'Fira Mono', 'Consolas', monospace;
}

::v-deep(.rich-text pre) {
  background: #f3f4f6;
  color: #334155;
  padding: 1em;
  border-radius: 0.5em;
  overflow-x: auto;
  margin: 1em 0;
  font-family: 'Fira Mono', 'Consolas', monospace;
  font-size: 0.95em;
}

::v-deep(.rich-text table) {
  width: 100%;
  border-collapse: collapse;
  margin: 1em 0;
}

::v-deep(.rich-text th),
::v-deep(.rich-text td) {
  border: 1px solid #e5e7eb;
  padding: 0.5em 1em;
  text-align: left;
}

::v-deep(.rich-text th) {
  background: #f1f5f9;
  font-weight: 600;
}

::v-deep(.rich-text hr) {
  border: none;
  border-top: 1px solid #e5e7eb;
  margin: 1.5em 0;
}

::v-deep(.rich-text ul ul),
::v-deep(.rich-text ol ol),
::v-deep(.rich-text ul ol),
::v-deep(.rich-text ol ul) {
  margin-bottom: 0;
  margin-top: 0.25rem;
}
</style>
