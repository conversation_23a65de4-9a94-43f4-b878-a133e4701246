<script setup>
import { onMounted } from 'vue'

import admissionsStore from '@/stores/admissionsStore.js'
import authStore from '@/stores/authStore.js'

// Initialize the store
const store = admissionsStore()

// Define columns for the table with grouping and custom widths
const columns = [
  { key: 'id', label: 'ID', rowspan: 2, width: 10 },
  {
    label: 'Student Information',
    colspan: 3,
    children: [
      { key: 'child_name', label: 'Child Name', sortable: true, width: 200 },
      { key: 'gender', label: 'Gender', width: 100 },
      { key: 'child_dob', label: 'Date of Birth', width: 150 }
    ]
  },
  {
    label: 'Parent Information',
    colspan: 2,
    children: [
      { key: 'parent_name', label: 'Parent Name', sortable: true, width: 200 },
      { key: 'relation', label: 'Relation', width: 120 },
      { key: 'phone_no', label: 'Phone Number', width: 150 }
    ]
  },
  { key: 'email', label: 'Email', rowspan: 2, width: 220 }
];

// Define additional filters (not tied to visible columns)
const additionalFilters = [
  {
    key: 'created_at',
    label: 'Created Date',
    type: 'daterange'
  },
  {
    key: 'updated_at',
    label: 'Last Updated',
    type: 'daterange'
  },
  {
    key: 'status',
    label: 'Application Status',
    type: 'select',
    options: [
      { value: 'pending', label: 'Pending' },
      { value: 'approved', label: 'Approved' },
      { value: 'rejected', label: 'Rejected' }
    ]
  },
  {
    key: 'assigned_to',
    label: 'Assigned To',
    type: 'select',
    options: [
      { value: 'user1', label: 'John Doe' },
      { value: 'user2', label: 'Jane Smith' },
      { value: 'user3', label: 'Robert Johnson' }
    ]
  }
];

// Filter configuration for columns
const filterConfig = {
  gender: {
    type: 'select',
    options: [
      { value: 'male', label: 'Male' },
      { value: 'female', label: 'Female' }
    ]
  },
  relation: {
    type: 'select',
    options: [
      { value: 'Father', label: 'Father' },
      { value: 'Mother', label: 'Mother' },
      { value: 'Guardian', label: 'Guardian' }
    ]
  },
  child_dob: {
    type: 'daterange'
  },
  child_name: {
    type: 'text'
  },
  parent_name: {
    type: 'text'
  }
};

// Handle export (just for logging purposes)
function handleExport(exportData) {
  console.log('Exporting data:', exportData);
}

// Load data when component mounts
onMounted(() => {
  store.fetchAdmissions();
  // authStore


});
</script>
<template>
  <div class="admissions-table-example">
    <h1>Admissions Data Table</h1>
    <p class="description">
      This example demonstrates how to use the DataTable component with a Laravel API response format.
      The component automatically handles pagination by sending requests to your API endpoint and
      correctly processes the nested response structure where items are in <code>data.data</code> array and
      pagination metadata includes <code>current_page</code>, <code>last_page</code>, <code>per_page</code>, and
      <code>total</code> fields.
    </p>
    <p class="description">
      This example also showcases the <strong>column spanning feature</strong>, which allows you to group related
      columns under common headers. Notice how "Student Information" and "Parent Information" span multiple columns,
      creating a more organized and hierarchical table structure.
    </p>
    <p class="description">
      Additionally, this example demonstrates <strong>row selection</strong>, <strong>export functionality</strong>,
      <strong>vertical scrolling</strong>, <strong>print functionality</strong>, <strong>additional
        filters</strong>, <strong>custom column widths</strong>, and <strong>searchable select dropdowns</strong>.
      You can select individual rows using checkboxes or use the "Select All" button in the header.
      Selected items can be exported using the export button, which allows you to choose between exporting all data
      or just the selected rows. The table has a fixed height with vertical scrolling enabled, and you can print the
      data using the print button in the header. The columns have custom widths specified, and the select dropdowns
      have search functionality to easily find options in long lists.
    </p>
    <p class="description">
      The <strong>additional filters</strong> feature allows you to filter by fields that aren't visible in the table,
      such as <code>created_at</code>, <code>updated_at</code>, <code>status</code>, and <code>assigned_to</code>.
      This is particularly useful for complex data sets where you need to filter by metadata or related fields
      that aren't displayed as columns.
    </p>

    <!-- Debug information -->
    <div class="debug-info">
      <p><strong>Loading:</strong> {{ store.loading }}</p>
      <p><strong>Current Page:</strong> {{ store.currentPage }}</p>
      <p><strong>Total Items:</strong> {{ store.totalItems }}</p>
      <p><strong>Items Per Page:</strong> {{ store.pageSize }}</p>
      <p><strong>Active Filters:</strong> {{ store.columnFilters &&
        Object.keys(store.columnFilters).length > 0 ?
        JSON.stringify(store.columnFilters) :
        'None' }}</p>
      <p><strong>Selected Items:</strong> {{ store.selectedItems ? store.selectedItems.length : 0 }}
        items</p>
      <div v-if="store.selectedItems && store.selectedItems.length > 0" class="selected-items-preview">
        <p><strong>Selected:</strong></p>
        <ul>
          <li v-for="item in (store.selectedItems ? store.selectedItems.slice(0, 3) : [])" :key="item.id">{{
            item.child_name }}</li>
          <li v-if="store.selectedItems && store.selectedItems.length > 3">... and {{
            store.selectedItems.length - 3 }}
            more</li>
        </ul>
      </div>
    </div>

    <!-- Admissions Data Table -->
    <div class="example-card">
      <h2>Admissions List</h2>

      <data-table :items="store.admissions" :columns="columns" title="Student Admissions" :loading="store.loading"
        :server-side="true" :total-items="store.totalItems" :current-page-server="store.currentPage"
        :page-size="store.pageSize" :show-column-filters="true" :selectable="true" :selected-items="store.selectedItems"
        :max-height="400" :filterable-columns="['gender', 'relation', 'child_dob', 'child_name', 'parent_name']"
        :filter-config="filterConfig" :additional-filters="additionalFilters" :show-additional-filters="true"
        :sticky-header="true" :sticky-filters="false"
        @page-change="store.handlePageChange" @page-size-change="store.handlePageSizeChange" @sort="store.handleSort"
        @search="store.handleSearch" @column-filter="store.handleColumnFilter"
        @update:selected="store.handleSelectionChange" @export="handleExport" @fetch-all-data="store.fetchAllData"
        theme="primary">
        <!-- Custom cell rendering for gender -->
        <template #cell-gender="{ value }">
          <span :class="['gender-badge', `gender-${value.toLowerCase()}`]">
            {{ value }}
          </span>
        </template>

        <!-- Custom cell rendering for child_dob -->
        <template #cell-child_dob="{ value }">
          {{ store.formatDate(value) }}
        </template>

        <!-- Custom actions column -->
        <template #actions="{ item }">
          <div class="action-buttons">
            <button class="action-button view-button" @click="store.viewAdmission(item)" title="View">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16">
                <path fill="none" d="M0 0h24v24H0z" />
                <path
                  d="M12 3c5.392 0 9.878 3.88 10.819 9-.94 5.12-5.427 9-10.819 9-5.392 0-9.878-3.88-10.819-9C2.121 6.88 6.608 3 12 3zm0 16a9.005 9.005 0 0 0 8.777-7 9.005 9.005 0 0 0-17.554 0A9.005 9.005 0 0 0 12 19zm0-2.5a4.5 4.5 0 1 1 0-9 4.5 4.5 0 0 1 0 9zm0-2a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5z"
                  fill="currentColor" />
              </svg>
            </button>
            <button class="action-button edit-button" @click="store.editAdmission(item)" title="Edit">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16">
                <path fill="none" d="M0 0h24v24H0z" />
                <path
                  d="M15.728 9.686l-1.414-1.414L5 17.586V19h1.414l9.314-9.314zm1.414-1.414l1.414-1.414-1.414-1.414-1.414 1.414 1.414 1.414zM7.242 21H3v-4.243L16.435 3.322a1 1 0 0 1 1.414 0l2.829 2.829a1 1 0 0 1 0 1.414L7.243 21z"
                  fill="currentColor" />
              </svg>
            </button>
            <button class="action-button delete-button" @click="store.deleteAdmission(item)" title="Delete">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16">
                <path fill="none" d="M0 0h24v24H0z" />
                <path
                  d="M7 4V2h10v2h5v2h-2v15a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1V6H2V4h5zM6 6v14h12V6H6zm3 3h2v8H9V9zm4 0h2v8h-2V9z"
                  fill="currentColor" />
              </svg>
            </button>
          </div>
        </template>

      </data-table>

      <!-- <div class="code-example">
        <h3>Code Example</h3>
        <pre><code>
      &lt;template&gt;
      &lt;data-table
      :items="store.admissions"
      :columns="columns"
      title="Student Admissions"
      :loading="store.loading"
      :server-side="true"
      :total-items="store.totalItems"
      :current-page-server="store.currentPage"
      :page-size="store.pageSize"
      :show-column-filters="true"
      :selectable="true"
      :selected-items="store.selectedItems"
      :max-height="400" // Enable vertical scrolling with fixed height
      :filterable-columns="['gender', 'relation', 'child_dob', 'child_name']"
      :additional-filters="additionalFilters"
      :show-additional-filters="true"
      :filter-config="filterConfig"
      @page-change="store.handlePageChange"
      @page-size-change="store.handlePageSizeChange"
      @sort="store.handleSort"
      @search="store.handleSearch"
      @column-filter="store.handleColumnFilter"
      @update:selected="store.handleSelectionChange"
      @export="handleExport"
      @fetch-all-data="store.fetchAllData"
      &gt;
      &lt;!-- Custom templates for cells and actions --&gt;
      &lt;/data-table&gt;
      &lt;/template&gt;

      &lt;script setup&gt;
      import { onMounted } from 'vue';
      import admissionsStore from '@/stores/admissionsStore';

      // Initialize the store
      const store = admissionsStore();

      // Define columns with grouping
      const columns = [
      { key: 'id', label: 'ID', rowspan: 2 },
      {
      label: 'Student Information',
      colspan: 3,
      children: [
      { key: 'child_name', label: 'Child Name', sortable: true },
      { key: 'gender', label: 'Gender' },
      { key: 'child_dob', label: 'Date of Birth' }
      ]
      },
      {
      label: 'Parent Information',
      colspan: 3,
      children: [
      { key: 'parent_name', label: 'Parent Name', sortable: true },
      { key: 'relation', label: 'Relation' },
      { key: 'phone_no', label: 'Phone Number' }
      ]
      },
      { key: 'email', label: 'Email', rowspan: 2 }
      ];

      // Handle export (just for logging purposes)
      function handleExport(exportData) {
      console.log('Exporting data:', exportData);
      }

      // Define additional filters (not tied to visible columns)
      const additionalFilters = [
      {
      key: 'created_at',
      label: 'Created Date',
      type: 'daterange'
      },
      {
      key: 'updated_at',
      label: 'Last Updated',
      type: 'daterange'
      },
      {
      key: 'status',
      label: 'Application Status',
      type: 'select',
      options: [
      { value: 'pending', label: 'Pending' },
      { value: 'approved', label: 'Approved' },
      { value: 'rejected', label: 'Rejected' }
      ]
      },
      {
      key: 'assigned_to',
      label: 'Assigned To',
      type: 'select',
      options: [
      { value: 'user1', label: 'John Doe' },
      { value: 'user2', label: 'Jane Smith' },
      { value: 'user3', label: 'Robert Johnson' }
      ]
      }
      ];

      // Filter configuration
      const filterConfig = {
      gender: {
      type: 'select',
      options: [
      { value: 'male', label: 'Male' },
      { value: 'female', label: 'Female' }
      ]
      },
      relation: {
      type: 'select',
      options: [
      { value: 'Father', label: 'Father' },
      { value: 'Mother', label: 'Mother' },
      { value: 'Guardian', label: 'Guardian' }
      ]
      },
      child_dob: {
      type: 'daterange'
      },
      child_name: {
      type: 'text'
      }
      };

      // Load data when component mounts
      onMounted(() => {
      store.fetchAdmissions();
      });
      &lt;/script&gt;
    </code></pre>
      </div> -->
    </div>
  </div>
</template>

<style>
.admissions-table-example {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

h1 {
  margin-bottom: 16px;
  color: #1e293b;
}

.description {
  margin-bottom: 24px;
  color: #64748b;
  font-size: 16px;
  line-height: 1.5;
}

.debug-info {
  margin-bottom: 24px;
  padding: 12px 16px;
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  color: #334155;
}

.debug-info p {
  margin: 4px 0;
}

.selected-items-preview {
  margin-top: 8px;
  padding: 8px;
  background-color: #f0f9ff;
  border-radius: 4px;
  border-left: 3px solid #3b82f6;
}

.selected-items-preview ul {
  margin: 4px 0 0 0;
  padding-left: 20px;
}

.selected-items-preview li {
  margin-bottom: 2px;
  font-size: 14px;
}

.example-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 24px;
  margin-bottom: 32px;
}

h2 {
  margin-top: 0;
  margin-bottom: 16px;
  color: #1e293b;
  font-size: 20px;
}

.code-example {
  margin-top: 24px;
  background-color: #f8fafc;
  border-radius: 6px;
  overflow: hidden;
}

.code-example h3 {
  margin: 0;
  padding: 12px 16px;
  background-color: #f1f5f9;
  color: #334155;
  font-size: 16px;
  font-weight: 600;
}

.code-example pre {
  margin: 0;
  padding: 16px;
  overflow-x: auto;
}

.code-example code {
  font-family: 'Courier New', Courier, monospace;
  font-size: 14px;
  color: #334155;
}

.gender-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.gender-male {
  background-color: #93c5fd;
  color: #1e40af;
}

.gender-female {
  background-color: #fbcfe8;
  color: #9d174d;
}



.company-info {
  display: flex;
  flex-direction: column;
}

.company-name {
  font-weight: 500;
}

.company-catchphrase {
  font-size: 12px;
  color: #64748b;
  font-style: italic;
  margin-top: 4px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.action-button {
  width: 28px;
  height: 28px;
  border-radius: 4px;
  border: none;
  background-color: #f8fafc;
  color: #64748b;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
}

.action-button:hover {
  background-color: #f1f5f9;
}

.view-button:hover {
  color: #3b82f6;
}

.edit-button:hover {
  color: #10b981;
}

.delete-button:hover {
  color: #ef4444;
}

/* Column filter styles */
.column-filters-row th {
  padding: 4px 16px 8px;
}

/* Custom styling for date filters */
.date-range-filter {
  display: flex;
  align-items: center;
  gap: 4px;
}

.date-range-filter .filter-date {
  flex: 1;
  min-width: 0;
  font-size: 12px;
  padding: 4px 6px;
}

/* Active filter styling */
.column-filter.active .filter-input,
.column-filter.active .filter-select,
.column-filter.active .filter-date {
  border-color: #3b82f6;
  background-color: rgba(59, 130, 246, 0.05);
}
</style>
