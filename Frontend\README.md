# Water Supply Management Board Website

This is a professional website for a Water Supply Management Board, built with HTML, CSS, and JavaScript using Tailwind CSS via CDN.

## Features

- Responsive design that works on mobile, tablet, and desktop
- Image slider with automatic rotation
- Mobile-friendly navigation menu
- "What's New" marquee section
- Structured for easy migration to Vue.js in the future

## Project Structure

```
├── index.html          # Main HTML file
├── css/
│   └── styles.css      # Custom CSS styles
├── js/
│   └── main.js         # JavaScript functionality
├── components/         # HTML components for easy Vue migration
│   ├── header.html     # Header component
│   ├── navigation.html # Navigation menu component
│   ├── slider.html     # Image slider component
│   ├── whats-new.html  # What's New marquee component
│   └── footer.html     # Footer component
└── images/             # Directory for images
    ├── logo.png        # Organization logo
    ├── banner.jpg      # Main banner image
    ├── banner2.jpg     # Second banner image
    └── banner3.jpg     # Third banner image
```

## Setup Instructions

1. Clone or download this repository
2. Add your images to the `images` directory
3. Open `index.html` in your browser to view the website

No build process is required as this project uses Tailwind CSS via CDN.

## Customization

### Changing Colors

The primary colors are defined in the Tailwind configuration in `index.html`:

```javascript
tailwind.config = {
    theme: {
        extend: {
            colors: {
                primary: '#1e3a8a', // dark blue
                secondary: '#0ea5e9', // light blue
                accent: '#f59e0b', // amber
            }
        }
    }
}
```

### Adding Content

To add more content to the website, modify the `index.html` file. The main content area is structured with a sidebar and main content section.

### Adding Slider Images

To add more slider images:

1. Add the image file to the `images` directory
2. Add a new slider item in the HTML
3. Add a corresponding dot in the slider navigation
4. Update the JavaScript to handle the new slide

## Future Vue.js Migration

This project is structured to make migration to Vue.js easier:

- Components are clearly separated in the HTML
- JavaScript functionality is modular
- Styles are organized for component-based architecture

When migrating to Vue.js:

1. Each section can become a Vue component
2. The slider can be converted to a reusable component
3. Navigation can be managed with Vue Router

## Credits

- [Tailwind CSS](https://tailwindcss.com/)
- [Font Awesome](https://fontawesome.com/)
